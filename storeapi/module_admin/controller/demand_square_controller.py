from fastapi import APIRouter, Depends, Query, Request, Form
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from config.get_db import get_db
from module_admin.entity.vo.demand_square_vo import (
    DemandSquareQueryVO,
    DemandSquareListResponseVO,
    DemandSquareStatsVO,
    DemandGrabRequestVO,
    DemandGrabResponseVO,
    ShareOrderRequestVO,
    StaffSelectionRequestVO
)
from module_admin.service.demand_square_service import DemandSquareService
from module_admin.service.internal_user_login_service import InternalUserLoginService
from utils.response_util import ResponseUtil
from utils.log_util import logger

# 创建路由器
demand_square_controller = APIRouter(prefix="/api/v1/demand-square", tags=["需求广场"])

@demand_square_controller.get("/list", summary="获取需求列表")
async def get_demand_list(
    page: int = Query(default=1, ge=1, description="页码"),
    size: int = Query(default=20, ge=1, le=100, description="每页数量"),
    business_type: Optional[int] = Query(default=None, description="业务类型：1-到家，2-到店"),
    demand_status: Optional[int] = Query(default=1, description="线索状态：1-待抢单，2-已抢单"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取需求列表
    
    Args:
        page: 页码，默认1
        size: 每页数量，默认20，最大100
        business_type: 业务类型，1-到家，2-到店，不传则查询全部
        demand_status: 线索状态，1-待抢单，2-已抢单，默认1
        db: 数据库会话
    
    Returns:
        需求列表数据
    """
    try:
        # 构建查询参数
        query_params = DemandSquareQueryVO(
            page=page,
            size=size,
            business_type=business_type,
            demand_status=demand_status
        )
        
        # 调用服务层获取数据
        result = await DemandSquareService.get_demand_list(db, query_params)
        
        logger.info(f"获取需求列表成功，页码: {page}, 数量: {len(result.list)}")
        return ResponseUtil.success(data=result.dict())
        
    except Exception as e:
        logger.error(f"获取需求列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取需求列表失败: {str(e)}")

@demand_square_controller.get("/stats", summary="获取需求统计信息")
async def get_demand_stats(
    business_type: Optional[int] = Query(default=None, description="业务类型：1-到家，2-到店"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取需求统计信息
    
    Args:
        business_type: 业务类型，1-到家，2-到店，不传则统计全部
        db: 数据库会话
    
    Returns:
        统计信息
    """
    try:
        # 调用服务层获取统计数据
        result = await DemandSquareService.get_demand_stats(db, business_type)
        
        logger.info(f"获取需求统计成功，总数: {result.total_count}")
        return ResponseUtil.success(data=result.dict())
        
    except Exception as e:
        logger.error(f"获取需求统计失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取需求统计失败: {str(e)}")


@demand_square_controller.post("/grab", summary="抢单")
async def grab_demand(
    request: Request,
    grab_data: DemandGrabRequestVO,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    抢单接口

    Args:
        request: FastAPI请求对象
        grab_data: 抢单请求数据
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        抢单结果
    """
    try:
        logger.info(f"收到抢单请求: demand_uuid={grab_data.demand_uuid}, store_uuid={grab_data.store_uuid}, user_id={current_user.user.id}")

        # 调用服务层处理抢单逻辑
        result = await DemandSquareService.grab_demand(
            db, grab_data, current_user
        )

        logger.info(f"抢单处理完成: {result}")

        if result.success:
            return ResponseUtil.success(data=result.dict(), msg=result.message)
        else:
            return ResponseUtil.error(msg=result.message)

    except Exception as e:
        logger.error(f"抢单请求处理失败: {str(e)}")
        return ResponseUtil.error(msg=f"抢单失败: {str(e)}")


@demand_square_controller.post("/share-order", summary="共享订单到家政广场")
async def share_order_to_square(
    order_id: int = Form(..., description="订单ID"),
    order_number: str = Form(..., description="订单号"),
    commission_amount: float = Form(..., description="佣金金额"),
    expire_hours: int = Form(default=48, description="过期小时数"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    将门店订单共享到家政广场

    Args:
        order_id: 订单ID
        order_number: 订单号
        commission_amount: 佣金金额
        expire_hours: 过期小时数，默认48小时
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        共享结果
    """
    try:
        logger.info(f"收到订单共享请求: order_id={order_id}, order_number={order_number}, commission_amount={commission_amount}, user_id={current_user.user.id}")

        # 构建共享请求数据
        share_request = ShareOrderRequestVO(
            order_id=order_id,
            order_number=order_number,
            commission_amount=commission_amount,
            expire_hours=expire_hours
        )

        # 调用服务层处理共享逻辑
        result = await DemandSquareService.share_order_to_square(
            db, share_request, current_user
        )

        logger.info(f"订单共享处理完成: {result}")

        if result.get('success', False):
            return ResponseUtil.success(msg=result.get('message', '订单共享成功'))
        else:
            return ResponseUtil.error(msg=result.get('message', '订单共享失败'))

    except Exception as e:
        logger.error(f"订单共享请求处理失败: {str(e)}")
        return ResponseUtil.error(msg=f"订单共享失败: {str(e)}")


@demand_square_controller.get("/available-staff", summary="获取门店可用员工列表")
async def get_available_staff_list(
    store_uuid: str = Query(..., description="门店UUID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    获取门店可用员工列表

    Args:
        store_uuid: 门店UUID
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        员工列表
    """
    try:
        logger.info(f"获取可用员工列表: store_uuid={store_uuid}, user_id={current_user.user.id}")

        # 调用服务层获取员工列表
        result = await DemandSquareService.get_available_staff_list(
            db, store_uuid, current_user
        )

        logger.info(f"获取员工列表成功，数量: {len(result.get('list', []))}")
        return ResponseUtil.success(data=result)

    except Exception as e:
        logger.error(f"获取员工列表失败: {str(e)}")
        return ResponseUtil.error(msg=f"获取员工列表失败: {str(e)}")


@demand_square_controller.post("/select-staff", summary="选择服务员工")
async def select_service_staff(
    demand_uuid: str = Form(..., description="需求UUID"),
    selected_staff_id: int = Form(..., description="选中的员工ID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    为抢单成功的共享订单选择服务员工

    Args:
        demand_uuid: 需求UUID
        selected_staff_id: 选中的员工ID
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        员工选择结果
    """
    try:
        logger.info(f"收到员工选择请求: demand_uuid={demand_uuid}, staff_id={selected_staff_id}, user_id={current_user.user.id}")

        # 构建员工选择请求数据
        staff_request = StaffSelectionRequestVO(
            demand_uuid=demand_uuid,
            selected_staff_id=selected_staff_id
        )

        # 调用服务层处理员工选择逻辑
        result = await DemandSquareService.select_service_staff(
            db, staff_request, current_user
        )

        logger.info(f"员工选择处理完成: {result}")

        if result.get('success', False):
            return ResponseUtil.success(msg=result.get('message', '员工选择成功'))
        else:
            return ResponseUtil.error(msg=result.get('message', '员工选择失败'))

    except Exception as e:
        logger.error(f"员工选择请求处理失败: {str(e)}")
        return ResponseUtil.error(msg=f"员工选择失败: {str(e)}")


@demand_square_controller.post("/cancel-grab", summary="取消抢单")
async def cancel_demand_grab(
    demand_uuid: str = Form(..., description="需求UUID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    取消抢单，回滚需求状态到待抢单

    Args:
        demand_uuid: 需求UUID
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        取消结果
    """
    try:
        logger.info(f"收到取消抢单请求: demand_uuid={demand_uuid}, user_id={current_user.user.id}")

        # 调用服务层处理取消抢单
        result = await DemandSquareService.cancel_demand_grab(
            db, demand_uuid, current_user
        )

        if result.get("success"):
            logger.info(f"取消抢单成功: {result}")
            return ResponseUtil.success(data=result, msg=result.get("message", "取消抢单成功"))
        else:
            logger.warning(f"取消抢单失败: {result}")
            return ResponseUtil.error(msg=result.get("message", "取消抢单失败"))

    except Exception as e:
        logger.error(f"取消抢单异常: {str(e)}")
        return ResponseUtil.error(msg=f"取消抢单失败: {str(e)}")


@demand_square_controller.post("/adjust-commission", summary="调整共享订单佣金")
async def adjust_shared_order_commission(
    order_id: int = Form(..., description="订单ID"),
    new_commission_amount: float = Form(..., description="新佣金金额"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    调整已共享订单的佣金金额

    Args:
        order_id: 订单ID
        new_commission_amount: 新佣金金额
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        调整结果
    """
    try:
        logger.info(f"收到佣金调整请求: order_id={order_id}, new_commission_amount={new_commission_amount}, user_id={current_user.user.id}")

        # 调用服务层处理佣金调整逻辑
        result = await DemandSquareService.adjust_shared_order_commission(
            db, order_id, new_commission_amount, current_user
        )

        logger.info(f"佣金调整处理完成: {result}")

        if result.get('success', False):
            return ResponseUtil.success(data=result, msg=result.get('message', '佣金调整成功'))
        else:
            return ResponseUtil.error(msg=result.get('message', '佣金调整失败'))

    except Exception as e:
        logger.error(f"佣金调整请求处理失败: {str(e)}")
        return ResponseUtil.error(msg=f"佣金调整失败: {str(e)}")


@demand_square_controller.get("/check-order-shared/{order_id}", summary="检查订单共享状态")
async def check_order_shared_status(
    order_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    检查订单是否已共享到家政人广场

    Args:
        order_id: 订单ID
        db: 数据库会话

    Returns:
        共享状态信息
    """
    try:
        logger.info(f"检查订单共享状态: order_id={order_id}")

        # 调用服务层检查共享状态
        result = await DemandSquareService.check_order_shared_status(db, order_id)

        if result.get("success"):
            return ResponseUtil.success(data=result, msg="查询成功")
        else:
            return ResponseUtil.error(msg=result.get("message", "查询失败"))

    except Exception as e:
        logger.error(f"检查订单共享状态异常: {str(e)}")
        return ResponseUtil.error(msg=f"查询失败: {str(e)}")


@demand_square_controller.post("/expire-shared-order", summary="取消共享订单")
async def expire_shared_order(
    order_id: int = Form(..., description="订单ID"),
    db: AsyncSession = Depends(get_db),
    current_user = Depends(InternalUserLoginService.get_current_user)
):
    """
    将共享订单设置为过期状态，取消共享

    Args:
        order_id: 订单ID
        db: 数据库会话
        current_user: 当前登录用户

    Returns:
        操作结果
    """
    try:
        logger.info(f"收到取消共享订单请求: order_id={order_id}, user_id={current_user.user.id}")

        # 调用服务层处理取消共享
        result = await DemandSquareService.expire_shared_order(
            db, order_id, current_user
        )

        if result.get("success"):
            logger.info(f"取消共享订单成功: {result}")
            return ResponseUtil.success(data=result, msg=result.get("message", "取消共享成功"))
        else:
            logger.warning(f"取消共享订单失败: {result}")
            return ResponseUtil.error(msg=result.get("message", "取消共享失败"))

    except Exception as e:
        logger.error(f"取消共享订单异常: {str(e)}")
        return ResponseUtil.error(msg=f"操作失败: {str(e)}")
