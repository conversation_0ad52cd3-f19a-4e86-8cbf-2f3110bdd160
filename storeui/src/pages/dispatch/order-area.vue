<template>
  <view class="order-area-page">
    <!-- 关闭订单确认弹窗 -->
    <u-popup :show="showCloseOrderPopup" @close="showCloseOrderPopup = false" mode="center" width="560rpx" height="auto" border-radius="16">
      <view class="close-order-popup">
        <view class="popup-title">确认关闭订单</view>
        <view class="popup-content">关闭订单后，该订单将无法继续操作，确认关闭吗？</view>
        <view class="popup-buttons">
          <view class="cancel-button" @click="cancelCloseOrder">取消</view>
          <view class="confirm-button" @click="confirmCloseOrder">确定</view>
        </view>
      </view>
    </u-popup>

    <!-- 支付方式选择弹窗 -->
    <PaymentMethodModal
      :visible="showPaymentModal"
      :orderInfo="currentPaymentOrder"
      @close="handlePaymentModalClose"
      @select="handlePaymentMethodSelect"
    />

    <!-- 扫码支付弹窗 -->
    <u-modal
      :show="showQrcodeModal"
      title="扫码支付"
      :showCancelButton="true"
      @cancel="closeQrcodeModal"
      @confirm="handlePaymentComplete"
      confirmText="完成支付"
    >
      <view class="qrcode-modal-content">
        <view class="qrcode-title">请使用微信扫描二维码完成支付</view>
        <view class="qrcode-container">
          <!-- 使用image组件显示二维码，如果失败则显示canvas -->
          <image
            v-if="qrcodeImageUrl && !useCanvasMode"
            :src="qrcodeImageUrl"
            class="qrcode-image"
            :style="{ width: qrcodeSize + 'px', height: qrcodeSize + 'px' }"
            mode="aspectFit"
            @error="handleQrcodeImageError"
          ></image>
          <canvas
            v-else
            canvas-id="qrcode-canvas"
            class="qrcode-canvas"
            :style="{ width: qrcodeSize + 'px', height: qrcodeSize + 'px' }"
          ></canvas>
        </view>
        <view class="qrcode-amount">支付金额：￥{{ currentOrderAmount }}</view>
        <view class="qrcode-tips">
          <text>• 请在30分钟内完成支付</text>
          <text>• 支付完成后请点击"完成支付"按钮</text>
        </view>

        <!-- 复制链接按钮 -->
        <view class="copy-link-section">
          <button class="copy-link-btn" @click="copyPayUrl">复制支付链接</button>
        </view>
      </view>
    </u-modal>

    <!-- 订单列表 - 包含搜索筛选和列表内容 -->
    <scroll-view
      scroll-y
      class="order-list"
      @scrolltolower="loadMoreOrders"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="refreshOrderList"
    >
      <!-- 搜索筛选容器 -->
      <view class="search-filter-container">
        <!-- 搜索框 -->
        <view class="search-box">
          <view class="search-input">
            <u-icon name="search" size="20" color="#999"></u-icon>
            <input
              type="text"
              placeholder="请输入订单ID, 客户姓名/手机号"
              v-model="searchKeyword"
              @input="onSearchInput"
              @confirm="performSearch"
            />
            <view class="search-clear" v-if="searchKeyword" @click="clearSearch">
              <u-icon name="close-circle" size="14" color="#999"></u-icon>
            </view>
          </view>
        </view>

        <!-- 筛选条件 -->
        <view class="filter-options">
          <view class="filter-item" @click="showStatusPicker = true">
            <text>{{ getSelectedStatusText() }}</text>
            <u-icon name="arrow-down" size="16" color="#666666"></u-icon>
          </view>
          <view class="filter-item" @click="showTypePicker = true">
            <text>{{ getSelectedTypeText() }}</text>
            <u-icon name="arrow-down" size="16" color="#666666"></u-icon>
          </view>
        </view>

        <!-- 筛选扩展区域 -->
        <view class="filter-extension">
          <!-- 时间分类 -->
          <view class="time-filter">
            <view
              class="time-filter-item"
              :class="{ active: selectedTimeFilter === item.value }"
              v-for="item in timeFilterOptions"
              :key="item.value"
              @click="selectTimeFilter(item.value)"
            >
              <text v-if="item.value !== 'custom'">{{ item.label }}</text>
              <text v-else>{{ getCustomTimeLabel() }}</text>
            </view>
          </view>

          <!-- 横向滚动状态筛选 -->
          <view class="horizontal-status-filter">
            <scroll-view scroll-x class="status-scroll">
              <view class="status-scroll-content">
                <view
                  class="status-filter-item"
                  :class="{ active: selectedHorizontalStatus === item.value }"
                  v-for="item in horizontalStatusOptions"
                  :key="item.value"
                  @click="selectHorizontalStatus(item.value)"
                >
                  <text>{{ item.label }}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
      <!-- 加载状态 - 骨架屏 -->
      <view v-if="loading && orderList.length === 0" class="loading-container">
        <!-- 骨架屏订单项 -->
        <view class="skeleton-order-item" v-for="n in 3" :key="n">
          <view class="skeleton-header">
            <view class="skeleton-line skeleton-time"></view>
            <view class="skeleton-line skeleton-status"></view>
          </view>
          <view class="skeleton-content">
            <view class="skeleton-line skeleton-type"></view>
            <view class="skeleton-line skeleton-product"></view>
            <view class="skeleton-line skeleton-service-time"></view>
            <view class="skeleton-line skeleton-customer"></view>
            <view class="skeleton-line skeleton-address"></view>
            <view class="skeleton-footer">
              <view class="skeleton-line skeleton-order-id"></view>
              <view class="skeleton-line skeleton-store"></view>
            </view>
          </view>
        </view>
        <!-- 传统加载提示 -->
        <view class="traditional-loading">
          <u-loading-icon mode="spinner" size="40"></u-loading-icon>
          <text class="loading-text">加载中...</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && orderList.length === 0" class="empty-container">
        <u-icon name="list" size="80" color="#ccc"></u-icon>
        <text class="empty-text">暂无订单数据</text>
      </view>

      <!-- 订单项 -->
      <view
        v-for="order in orderList"
        :key="order.order_number"
        class="order-item"
        @click="goToOrderDetail(order)"
      >
        <view class="order-time">
          <text>创建时间: {{ formatTime(order.create_time) }}</text>
          <view class="order-status">
            <view class="status-tag-container">
              <view
                class="status-tag"
                :class="{
                  'waiting-payment': order.order_status === -1,
                  'paid': order.order_status === 10,
                  'dispatch-confirm': order.order_status === 20,
                  'rejected': order.order_status === 30,
                  'dispatched': order.order_status === 40,
                  'in-progress': order.order_status === 50,
                  'service-started': order.order_status === 60,
                  'service-ended': order.order_status === 70,
                  'completed': order.order_status === 80,
                  'reviewed': order.order_status === 90,
                  'cancelled': order.order_status === 99
                }"
              >
                <text class="status-text">{{ getStatusText(order.order_status) }}</text>
              </view>
              <text class="order-amount">¥{{ order.total_pay_actual || order.pay_actual || 0 }}</text>
            </view>
          </view>
        </view>

        <view class="order-content">
          <view class="order-type">
            <u-icon name="home" size="16" color="#FF801B"></u-icon>
            <text class="type-name">{{ order.product_name || '未知产品' }} ({{ order.type_name || getTypeText(order.product_type) }})</text>
          </view>

          <view class="order-detail-item">
            <u-icon name="checkbox-mark" size="16" color="#999"></u-icon>
            <text class="detail-text">{{ order.product_name || '未知产品' }}</text>
          </view>

          <view class="order-detail-item" v-if="order.service_date">
            <u-icon name="calendar" size="16" color="#999"></u-icon>
            <text class="detail-text">服务时间: {{ formatServiceTime(order.service_date, order.service_hour) }}</text>
            <text class="copy-info-btn" @click.stop="copyOrderInfo(order)">复制信息</text>
          </view>

          <view class="order-detail-item">
            <u-icon name="phone" size="16" color="#999"></u-icon>
            <text class="detail-text">
              客户信息:
              <text v-if="order.customer_name">{{ order.customer_name }}</text>
              <text v-else-if="order.customer_mobile">{{ order.customer_mobile }}</text>
              <text v-else>用户ID: {{ order.user_id }}</text>
              <text v-if="order.customer_name && order.customer_mobile"> ({{ order.customer_mobile }})</text>
            </text>
            <text v-if="order.customer_mobile" class="contact-btn" @click.stop="contactCustomer(order.customer_mobile)">联系</text>
          </view>

          <view class="order-detail-item" v-if="order.service_address">
            <u-icon name="map" size="16" color="#999"></u-icon>
            <text class="detail-text">{{ order.service_address.trim() || '地址未填写' }}</text>
          </view>

          <view class="order-detail-item" v-if="order.service_remark">
            <text class="remark-label">备注:</text>
            <text class="remark-content">{{ order.service_remark }}</text>
          </view>

          <view class="order-detail-item" v-if="order.remark">
            <text class="remark-label">订单备注:</text>
            <text class="remark-content">{{ order.remark }}</text>
          </view>

          <view class="order-id">
            <text class="id-label">订单号</text>
            <text class="id-value">{{ order.order_number }}</text>
            <text class="store-info">{{ order.store_name || '未知门店' }}</text>
          </view>
        </view>

        <!-- 待付款特别提示区域 -->
        <view v-if="order.pay_status === 0 || order.pay_status === null" class="payment-pending-section">
          <view class="payment-pending-info">
            <text class="payment-pending-icon">💰</text>
            <text class="payment-pending-message">该订单尚未付款，请及时处理</text>
          </view>
          <button class="payment-pending-btn" @click.stop="handlePayment(order)">
            立即扣费
          </button>
        </view>

        <!-- 订单操作按钮 -->
        <view class="order-actions" v-if="order.order_status !== 80 && order.order_status !== 99">
          <!-- 改时/改价按钮 - 根据不同状态显示不同功能 -->
          <view
            v-if="order.order_status === 10"
            class="action-btn edit-order-btn"
            @click.stop="openEditOrderPopup(order)"
          >
            改时/改价
          </view>
          <!-- 改时/改价按钮 - 非已完成和已取消状态且不等于10时也可改时改价 -->
          <view
            v-else-if="order.order_status !== 80 && order.order_status !== 99"
            class="action-btn edit-order-btn"
            @click.stop="openEditOrderPopup(order)"
          >
            改时/改价
          </view>

          <!-- 取消订单按钮 - 状态码<30或状态为40（已派单）时显示 -->
          <view
            v-if="order.order_status < 30 || order.order_status === 40"
            class="action-btn cancel-btn"
            @click.stop="cancelOrder(order)"
          >
            取消订单
          </view>

          <!-- 状态为10（已接单）时显示派单按钮 -->
          <view
            v-if="order.order_status === 10"
            class="action-btn new-appointment-btn"
            @click.stop="dispatchOrder(order)"
          >
            去派单
          </view>

          <!-- 非已完成、已取消、已接单状态时显示重派按钮 -->
          <view
            v-if="order.order_status !== 80 && order.order_status !== 99 && order.order_status !== 10"
            class="action-btn new-appointment-btn"
            @click.stop="redispatchOrder(order)"
          >
            {{ order.order_status === 40 ? '修改派单' : '重新派单' }}
          </view>

        </view>

        <!-- 共享到家政广场按钮 - 单独占一行，在订单卡片底部 -->
        <view
          v-if="canShareOrder(order)"
          class="share-to-square-container"
        >
          <!-- 未共享时显示共享按钮 -->
          <view
            v-if="!isOrderShared(order)"
            class="share-to-square-btn"
            @click.stop="shareOrderToSquare(order)"
          >
            <u-icon name="share" size="16" color="#fff"></u-icon>
            <text>共享到家政广场</text>
          </view>

          <!-- 已共享时显示状态和操作按钮 -->
          <view v-else class="shared-order-actions">
            <view class="shared-status-info">
              <u-icon name="checkmark-circle" size="16" color="#28a745"></u-icon>
              <text class="shared-status-text">{{ getShareButtonText(order) }}</text>
            </view>

            <!-- 待抢单状态下显示调整佣金按钮 -->
            <view
              v-if="canAdjustCommission(order)"
              class="adjust-commission-btn"
              @click.stop="openAdjustCommissionModal(order)"
            >
              <u-icon name="edit-pen" size="14" color="#fff"></u-icon>
              <text>调整到手金额</text>
            </view>
          </view>
        </view>


      </view>

      <!-- 加载更多状态 -->
      <view v-if="loadingMore" class="loading-more">
        <u-loading-icon mode="spinner" size="20"></u-loading-icon>
        <text class="loading-more-text">加载更多...</text>
      </view>

      <!-- 没有更多数据 -->
      <view v-if="noMoreData && orderList.length > 0" class="no-more-data">
        <text>没有更多数据了</text>
      </view>
    </scroll-view>

    <!-- 订单状态选择器 -->
    <u-picker
      :show="showStatusPicker"
      :columns="[statusOptions]"
      keyName="label"
      @confirm="confirmStatus"
      @cancel="showStatusPicker = false"
      confirmColor="#fdd118"
    ></u-picker>

    <!-- 订单类型选择器 -->
    <u-picker
      :show="showTypePicker"
      :columns="[typeOptions]"
      keyName="label"
      @confirm="confirmType"
      @cancel="showTypePicker = false"
      confirmColor="#fdd118"
    ></u-picker>

    <!-- 自定义时间范围选择弹窗 -->
    <u-popup
      :show="showCustomDatePicker"
      @close="showCustomDatePicker = false"
      mode="center"
      :customStyle="{ width: '90%', maxWidth: '600rpx' }"
      round="16"
    >
      <view class="custom-date-picker">
        <view class="picker-header">
          <text class="picker-title">选择时间范围</text>
        </view>

        <view class="date-range-container">
          <view class="date-item">
            <text class="date-label">开始日期</text>
            <view class="date-input" @click="showStartDatePicker">
              <text class="date-text">{{ customStartDate || '请选择开始日期' }}</text>
              <u-icon name="calendar" size="20" color="#999"></u-icon>
            </view>
          </view>

          <view class="date-item">
            <text class="date-label">结束日期</text>
            <view class="date-input" @click="showEndDatePicker">
              <text class="date-text">{{ customEndDate || '请选择结束日期' }}</text>
              <u-icon name="calendar" size="20" color="#999"></u-icon>
            </view>
          </view>
        </view>

        <view class="picker-buttons">
          <view class="picker-cancel" @click="cancelCustomDate">取消</view>
          <view class="picker-confirm" @click="confirmCustomDate">确定</view>
        </view>
      </view>
    </u-popup>

    <!-- 开始日期选择器 -->
    <u-datetime-picker
      :show="showStartPicker"
      v-model="tempStartDate"
      mode="date"
      @confirm="confirmStartDate"
      @cancel="showStartPicker = false"
      confirmColor="#fdd118"
    ></u-datetime-picker>

    <!-- 结束日期选择器 -->
    <u-datetime-picker
      :show="showEndPicker"
      v-model="tempEndDate"
      mode="date"
      @confirm="confirmEndDate"
      @cancel="showEndPicker = false"
      confirmColor="#fdd118"
    ></u-datetime-picker>

    <!-- 改时/改价弹窗 - 恢复u-modal版本 -->
    <u-modal
      :show="showEditOrderPopup"
      title="修改订单信息"
      :showCancelButton="true"
      @cancel="closeEditOrderPopup"
      @confirm="confirmEditOrder"
      confirmText="确认修改"
      cancelText="取消"
      width="600rpx"
    >
      <view class="edit-order-modal" v-if="showEditOrderPopup">
        <!-- 订单信息显示 -->
        <view class="order-info-section">
          <view class="info-row">
            <text class="info-label">订单号：</text>
            <text class="info-value">{{ currentEditOrder.order_number || '' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">当前金额：</text>
            <text class="info-value amount">¥{{ currentEditOrder.total_pay_actual || currentEditOrder.pay_actual || '0.00' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">服务时间：</text>
            <text class="info-value">{{ formatServiceTime(currentEditOrder.service_date, currentEditOrder.service_hour) }}</text>
          </view>
        </view>

        <!-- 修改金额 -->
        <view class="edit-section">
          <view class="section-title">修改订单金额</view>
          <view class="form-item-wrapper">
            <u-input 
              v-model="newAmount" 
              type="digit"
              placeholder="请输入新的订单金额"
              border="surround"
              @change="validateAmount"
              @focus="onAmountFocus"
              @blur="onAmountBlur"
            >
              <template slot="prefix">
                <view class="input-prefix">¥</view>
              </template>
            </u-input>
          </view>
          <view class="input-hint">支持小数点后两位，例如：99.99</view>
        </view>

        <!-- 修改时间 - 根据订单状态显示 -->
        <view class="edit-section" v-if="currentEditOrder.order_status !== 80 && currentEditOrder.order_status !== 99">
          <view class="section-title">修改服务时间</view>
          
          <view class="time-selectors-container">
            <!-- 服务日期选择 -->
            <view class="form-item-wrapper" @click="showServiceDatePicker">
              <u-input 
                v-model="newServiceDate" 
                placeholder="请选择服务日期"
                readonly
                border="surround"
              >
                <template slot="suffix">
                  <u-icon name="calendar" color="#c8c9cc" size="32rpx"></u-icon>
                </template>
              </u-input>
            </view>
            
            <!-- 服务时间选择 -->
            <view class="form-item-wrapper" @click="showServiceHourPicker">
              <u-input 
                v-model="formattedServiceHour" 
                placeholder="请选择服务时间"
                readonly
                border="surround"
              >
                <template slot="suffix">
                  <u-icon name="clock" color="#c8c9cc" size="32rpx"></u-icon>
                </template>
              </u-input>
            </view>
          </view>
          
          <view class="input-hint">可以只修改金额或时间，也可以同时修改</view>
        </view>

        <!-- 状态提示 -->
        <view class="warning-tip" v-if="currentEditOrder.order_status === 80 || currentEditOrder.order_status === 99">
          <text class="warning-icon">⚠️</text>
          <text class="warning-text">当前订单状态只允许修改金额，不能修改服务时间</text>
        </view>
      </view>
    </u-modal>



    <!-- 服务日期选择器 -->
    <u-datetime-picker
      :show="showServiceDatePickerModal"
      v-model="tempServiceDate"
      mode="date"
      :min-date="minServiceDate"
      @confirm="confirmServiceDate"
      @cancel="showServiceDatePickerModal = false"
      confirmColor="#fdd118"
    ></u-datetime-picker>



    <!-- 服务小时选择器 -->
    <u-picker
      :show="showServiceHourPickerModal"
      :columns="[hourOptions]"
      keyName="label"
      @confirm="confirmServiceHour"
      @cancel="showServiceHourPickerModal = false"
      confirmColor="#fdd118"
    ></u-picker>

    <!-- 佣金调整弹窗 -->
    <u-popup
      :show="showAdjustCommissionModal"
      @close="closeAdjustCommissionModal"
      mode="center"
      :customStyle="{ width: '85%', maxWidth: '600rpx' }"
      round="16"
    >
      <view class="adjust-commission-popup">
        <view class="popup-header">
          <text class="popup-title">调整到手金额</text>
          <u-icon name="close" size="20" color="#999" @click="closeAdjustCommissionModal"></u-icon>
        </view>

        <view class="popup-content">
          <!-- 订单信息 -->
          <view class="order-info-section" v-if="currentAdjustOrder">
            <view class="info-row">
              <text class="info-label">订单号：</text>
              <text class="info-value">{{ currentAdjustOrder.order_number || 'N/A' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">服务项目：</text>
              <text class="info-value">{{ currentAdjustOrder.product_name || 'N/A' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">订单金额：</text>
              <text class="info-value amount">¥{{ currentAdjustOrder.pay_actual || '0.00' }}</text>
            </view>
          </view>

          <!-- 佣金调整 -->
          <view class="commission-adjust-section">
            <view class="current-commission">
              <text class="section-label">当前到手金额：</text>
              <text class="current-amount">¥{{ currentCommissionAmount.toFixed(2) }}</text>
            </view>

            <view class="new-commission">
              <text class="section-label">新到手金额</text>
              <view class="input-container">
                <text class="currency-symbol">¥</text>
                <input
                  type="digit"
                  v-model="newCommissionAmount"
                  placeholder="0.00"
                  class="amount-input"
                  @input="validateNewCommissionInput"
                />
              </view>
              <text class="input-tip">新到手金额必须大于当前到手金额，且不能超过订单总金额</text>
            </view>
          </view>
        </view>

        <view class="popup-buttons">
          <view class="popup-cancel" @click="closeAdjustCommissionModal">取消</view>
          <view class="popup-confirm" @click="confirmAdjustCommission">确认调整</view>
        </view>
      </view>
    </u-popup>

    <!-- 共享订单设置弹窗 -->
    <u-popup
      :show="showShareSettingModal"
      @close="closeShareSettingModal"
      mode="center"
      :customStyle="{ width: '90%', maxWidth: '700rpx' }"
      round="16"
    >
      <view class="share-setting-popup">
        <view class="popup-header">
          <text class="popup-title">共享订单设置</text>
          <u-icon name="close" size="20" color="#999" @click="closeShareSettingModal"></u-icon>
        </view>

        <view class="popup-content">
          <!-- 订单信息 -->
          <view class="order-info-section" v-if="currentShareOrder">
            <view class="info-row">
              <text class="info-label">订单号：</text>
              <text class="info-value">{{ currentShareOrder.order_number || 'N/A' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">服务项目：</text>
              <text class="info-value">{{ currentShareOrder.product_name || 'N/A' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">订单金额：</text>
              <text class="info-value amount">¥{{ currentShareOrder.pay_actual || '0.00' }}</text>
            </view>
          </view>

          <!-- 佣金设置 -->
          <view class="setting-section">
            <text class="section-title">到手金额设置</text>
            <view class="input-container">
              <text class="currency-symbol">¥</text>
              <input
                type="digit"
                v-model="shareCommissionAmount"
                placeholder="0.00"
                class="amount-input"
                @input="validateShareCommissionInput"
              />
            </view>
            <text class="input-tip">到手金额不能超过订单总金额</text>
          </view>

          <!-- 过期时间设置 -->
          <view class="setting-section">
            <text class="section-title">过期时间设置</text>
            <view class="time-options">
              <view
                class="time-option"
                v-for="option in shareTimeOptions"
                :key="option.value"
                :class="{ active: shareExpireHours === option.value }"
                @click="selectShareExpireTime(option.value)"
              >
                <text class="option-text">{{ option.label }}</text>
                <view class="option-radio" :class="{ checked: shareExpireHours === option.value }">
                  <view class="radio-dot" v-if="shareExpireHours === option.value"></view>
                </view>
              </view>
            </view>

            <!-- 自定义时间输入 -->
            <view class="custom-time-section" v-if="shareExpireHours === 'custom'">
              <view class="custom-input-row">
                <input
                  type="number"
                  v-model="shareCustomHours"
                  placeholder="请输入小时数"
                  class="custom-time-input"
                  @input="validateShareCustomTime"
                />
                <text class="time-unit">小时</text>
              </view>
              <text class="custom-tip">自定义时间范围：1-168小时（7天）</text>
            </view>
          </view>
        </view>

        <view class="popup-buttons">
          <view class="popup-cancel" @click="closeShareSettingModal">取消</view>
          <view class="popup-confirm" @click="confirmShareOrder">确认共享</view>
        </view>
      </view>
    </u-popup>

  </view>
</template>

<script>
import { findOrderList, updateOrderAmount, updateOrderTime, cancelOrder } from '@/api/order.js'
import { shareOrderToDemandSquare, checkOrderSharedStatus, expireSharedOrder, adjustSharedOrderCommission } from '@/api/demand-square.js'
import PaymentMethodModal from '@/components/PaymentMethodModal.vue'

export default {
  components: {
    PaymentMethodModal
  },
  computed: {
    // 格式化显示的服务时间
    formattedServiceHour() {
      if (!this.newServiceHour) return '';
      return this.newServiceHour + ':00';
    }
  },
  data() {
    return {
      // 搜索相关
      searchKeyword: '',
      searchTimer: null,

      // 筛选相关
      selectedStatus: '',
      selectedType: '',
      showStatusDropdown: false,
      showTypeDropdown: false,
      showStatusPicker: false,
      showTypePicker: false,

      // 扩展筛选相关
      selectedTimeFilter: '',
      selectedHorizontalStatus: '',

      // 自定义时间范围相关
      showCustomDatePicker: false,
      customStartDate: '',
      customEndDate: '',
      customDateRange: '',
      showStartPicker: false,
      showEndPicker: false,
      tempStartDate: new Date().getTime(),
      tempEndDate: new Date().getTime(),

      // 下拉选项数据
      statusOptions: [
        { label: '全部', value: '' },
        { label: '已接单', value: '10' },
        { label: '派单待确认', value: '20' },
        { label: '拒绝接单', value: '30' },
        { label: '已派单', value: '40' },
        { label: '执行中', value: '50' },
        { label: '开始服务', value: '60' },
        { label: '服务结束', value: '70' },
        { label: '已完成', value: '80' },
        { label: '已评价', value: '90' },
        { label: '已取消', value: '99' }
      ],
      typeOptions: [
        { label: '全部项目', value: '' },
        { label: '单次项目', value: '1' },
        { label: '套餐项目', value: '2' }
      ],

      // 时间筛选选项
      timeFilterOptions: [
        { label: '全部', value: '' },
        { label: '今天', value: 'today' },
        { label: '昨天', value: 'yesterday' },
        { label: '明天', value: 'tomorrow' },
        { label: '自定义', value: 'custom' }
      ],

      // 横向状态筛选选项
      horizontalStatusOptions: [
        { label: '全部状态', value: '' },
        { label: '已接单', value: '10' },
        { label: '派单待确认', value: '20' },
        { label: '拒绝接单', value: '30' },
        { label: '已派单', value: '40' },
        { label: '执行中', value: '50' },
        { label: '开始服务', value: '60' },
        { label: '服务结束', value: '70' },
        { label: '已完成', value: '80' },
        { label: '已评价', value: '90' },
        { label: '已取消', value: '99' }
      ],

      // 订单数据
      orderList: [],
      orderStats: {
        total_orders: 0,
        pending_payment: 0,
        paid: 0,
        in_service: 0,
        completed: 0,
        cancelled: 0,
        refunded: 0
      },

      // 分页相关
      currentPage: 1,
      pageSize: 20,
      total: 0,
      loading: true, // 初始设为 true，确保页面加载时立即显示加载状态
      loadingMore: false,
      noMoreData: false,
      refreshing: false,

      // 弹窗相关
      showCloseOrderPopup: false,
      currentOrderId: '',

      // 支付弹窗相关
      showPaymentModal: false,
      currentPaymentOrder: null,

      // 扫码支付相关
      showQrcodeModal: false,
      payUrl: '',
      currentOrderNumber: '',
      currentPaymentOrderNumber: '', // 支付订单号
      currentOrderAmount: 0, // 当前订单金额
      qrcodeSize: 200,
      qrcodeImageUrl: '', // 二维码图片URL
      useCanvasMode: false, // 是否使用canvas模式
      pollingTimer: null,
      pollingCount: 0,
      maxPollingCount: 150, // 最大轮询次数（5分钟，每2秒一次）

      // 改时/改价弹窗相关
      showEditOrderPopup: false,
      currentEditOrder: {},
      newAmount: '',
      newServiceDate: '',
      newServiceHour: '',
      showServiceDatePickerModal: false,
      showServiceHourPickerModal: false,
      tempServiceDate: new Date().getTime(),
      minServiceDate: (() => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return today.getTime();
      })(),

      // 小时选项
      hourOptions: [
        { label: '08:00', value: '08' },
        { label: '09:00', value: '09' },
        { label: '10:00', value: '10' },
        { label: '11:00', value: '11' },
        { label: '12:00', value: '12' },
        { label: '13:00', value: '13' },
        { label: '14:00', value: '14' },
        { label: '15:00', value: '15' },
        { label: '16:00', value: '16' },
        { label: '17:00', value: '17' },
        { label: '18:00', value: '18' },
        { label: '19:00', value: '19' },
        { label: '20:00', value: '20' },
        { label: '21:00', value: '21' },
        { label: '22:00', value: '22' }
      ],

      // 共享设置弹窗相关
      showShareSettingModal: false,
      currentShareOrder: null,
      shareCommissionAmount: '',
      shareExpireHours: 48, // 默认48小时
      shareCustomHours: '',
      shareTimeOptions: [
        { label: '1小时', value: 1 },
        { label: '2小时', value: 2 },
        { label: '6小时', value: 6 },
        { label: '12小时', value: 12 },
        { label: '24小时', value: 24 },
        { label: '48小时', value: 48 },
        { label: '72小时', value: 72 },
        { label: '自定义', value: 'custom' }
      ],

      // 佣金调整弹窗相关
      showAdjustCommissionModal: false,
      currentAdjustOrder: null,
      newCommissionAmount: '',
      currentCommissionAmount: 0

    };
  },

  mounted() {
    this.loadOrderList();
  },

  onShow() {
    // 每次页面显示时刷新订单列表
    this.loadOrderList(true);
  },

  beforeDestroy() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    // 清理支付状态轮询定时器
    this.stopPaymentStatusPolling();
  },

  methods: {
    // 公共刷新方法，供父组件调用
    refreshOrderData() {
      this.loadOrderList(true);
    },

    // 加载订单列表
    async loadOrderList(refresh = false) {
      // 防止重复请求，但允许首次加载
      if (this.loading && this.orderList.length > 0) {
        return;
      }

      try {
        this.loading = true;

        if (refresh) {
          this.currentPage = 1;
          this.noMoreData = false;
        }

        const params = {
          page: this.currentPage,
          size: this.pageSize
        };

        // 只添加有值的参数，避免传递 undefined
        if (this.selectedType && this.selectedType.trim()) {
          params.product_type = this.selectedType;
        }

        // 添加状态筛选参数
        if (this.selectedStatus && this.selectedStatus.trim()) {
          params.order_status = this.selectedStatus;
        }

        // 添加搜索关键词参数
        if (this.searchKeyword && this.searchKeyword.trim()) {
          params.keyword = this.searchKeyword.trim();
        }

        // 注意：时间筛选在前端处理，不传递给后端

        // 添加横向状态筛选参数（优先级高于原状态筛选）
        if (this.selectedHorizontalStatus && this.selectedHorizontalStatus.trim()) {
          params.order_status = this.selectedHorizontalStatus;
        }


        const response = await findOrderList(params);

        const { list, total } = response;

        // 对获取的订单数据进行时间筛选
        const filteredList = this.filterOrdersByTime(list || []);

        if (refresh || this.currentPage === 1) {
          this.orderList = filteredList;
        } else {
          this.orderList = [...this.orderList, ...filteredList];
        }

        // 异步检查订单的共享状态（仅对可共享的订单）
        this.checkOrdersSharedStatus();

        // 注意：由于前端筛选，total 和 noMoreData 的逻辑需要调整
        this.total = filteredList.length;
        this.noMoreData = filteredList.length < this.pageSize;




      } catch (error) {
        uni.showToast({
          title: '加载订单失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        this.loadingMore = false;
        this.refreshing = false;
      }
    },

    // 搜索相关方法
    onSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置防抖，500ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.performSearch();
      }, 500);
    },

    performSearch() {
      this.loadOrderList(true);
    },

    clearSearch() {
      this.searchKeyword = '';
      this.loadOrderList(true);
    },
    // 筛选相关方法
    toggleStatusDropdown() {
      this.showStatusDropdown = !this.showStatusDropdown;
      if (this.showStatusDropdown) {
        this.showTypeDropdown = false;
      }
    },

    toggleTypeDropdown() {
      this.showTypeDropdown = !this.showTypeDropdown;
      if (this.showTypeDropdown) {
        this.showStatusDropdown = false;
      }
    },

    selectStatus(status) {
      this.selectedStatus = status;
      this.showStatusDropdown = false;
      this.loadOrderList(true);
    },

    selectType(type) {
      this.selectedType = type;
      this.showTypeDropdown = false;
      this.loadOrderList(true);
    },

    // 获取选中状态文本
    getSelectedStatusText() {
      const option = this.statusOptions.find(item => item.value === this.selectedStatus);
      return option ? option.label : '订单状态';
    },

    // 获取选中类型文本
    getSelectedTypeText() {
      const option = this.typeOptions.find(item => item.value === this.selectedType);
      return option ? option.label : '订单类型';
    },

    // 确认状态选择
    confirmStatus(e) {
      const selectedOption = e.value[0];
      this.selectedStatus = selectedOption.value;
      this.showStatusPicker = false;
      this.loadOrderList(true);
    },

    // 确认类型选择
    confirmType(e) {
      const selectedOption = e.value[0];
      this.selectedType = selectedOption.value;
      this.showTypePicker = false;
      this.loadOrderList(true);
    },

    // 时间筛选处理
    selectTimeFilter(timeValue) {
      if (timeValue === 'custom') {
        this.showCustomDatePicker = true;
      } else {
        this.selectedTimeFilter = timeValue;
        this.loadOrderList(true);
      }
    },

    // 横向状态筛选处理
    selectHorizontalStatus(statusValue) {
      this.selectedHorizontalStatus = statusValue;
      this.loadOrderList(true);
    },

    // 获取时间筛选的日期范围
    getTimeFilterDateRange(timeFilter) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      switch (timeFilter) {
        case 'today':
          return {
            start: today,
            end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
          };
        case 'yesterday':
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
          return {
            start: yesterday,
            end: new Date(yesterday.getTime() + 24 * 60 * 60 * 1000 - 1)
          };
        case 'tomorrow':
          const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
          return {
            start: tomorrow,
            end: new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000 - 1)
          };
        case 'custom':
          if (this.customStartDate && this.customEndDate) {
            const startDate = new Date(this.customStartDate);
            const endDate = new Date(this.customEndDate);
            // 设置结束日期为当天的23:59:59
            endDate.setHours(23, 59, 59, 999);
            return {
              start: startDate,
              end: endDate
            };
          }
          return null;
        default:
          return null;
      }
    },

    // 格式化日期为 YYYY-MM-DD 格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取自定义时间标签显示
    getCustomTimeLabel() {
      if (this.selectedTimeFilter === 'custom' && this.customStartDate && this.customEndDate) {
        return `${this.customStartDate} 至 ${this.customEndDate}`;
      }
      return '自定义';
    },

    // 显示开始日期选择器
    showStartDatePicker() {
      // 如果已有自定义开始日期，使用它；否则使用当前日期
      if (this.customStartDate) {
        this.tempStartDate = new Date(this.customStartDate).getTime();
      } else {
        this.tempStartDate = new Date().getTime();
      }
      this.showStartPicker = true;
    },

    // 显示结束日期选择器
    showEndDatePicker() {
      // 如果已有自定义结束日期，使用它；否则使用当前日期
      if (this.customEndDate) {
        this.tempEndDate = new Date(this.customEndDate).getTime();
      } else {
        this.tempEndDate = new Date().getTime();
      }
      this.showEndPicker = true;
    },

    // 确认开始日期
    confirmStartDate(e) {
      this.customStartDate = this.formatDate(new Date(e.value));
      this.showStartPicker = false;
    },

    // 确认结束日期
    confirmEndDate(e) {
      this.customEndDate = this.formatDate(new Date(e.value));
      this.showEndPicker = false;
    },

    // 取消自定义日期选择
    cancelCustomDate() {
      this.showCustomDatePicker = false;
    },

    // 确认自定义日期范围
    confirmCustomDate() {
      if (!this.customStartDate || !this.customEndDate) {
        uni.showToast({
          title: '请选择完整的时间范围',
          icon: 'none'
        });
        return;
      }

      // 验证日期范围
      const startDate = new Date(this.customStartDate);
      const endDate = new Date(this.customEndDate);

      if (startDate > endDate) {
        uni.showToast({
          title: '开始日期不能晚于结束日期',
          icon: 'none'
        });
        return;
      }

      this.selectedTimeFilter = 'custom';
      this.showCustomDatePicker = false;
      this.loadOrderList(true);
    },

    // 前端时间筛选方法
    filterOrdersByTime(orderList) {
      if (!this.selectedTimeFilter || this.selectedTimeFilter === '') {
        return orderList; // 没有时间筛选，返回全部
      }

      const dateRange = this.getTimeFilterDateRange(this.selectedTimeFilter);
      if (!dateRange) {
        return orderList; // 无效的时间范围，返回全部
      }

      return orderList.filter(order => {
        if (!order.create_time) {
          return false; // 没有创建时间的订单不显示
        }

        try {
          const orderDate = new Date(order.create_time);
          return orderDate >= dateRange.start && orderDate <= dateRange.end;
        } catch (error) {
          console.error('解析订单创建时间失败:', order.create_time, error);
          return false;
        }
      });
    },

    // 下拉刷新
    refreshOrderList() {
      this.refreshing = true;
      this.currentPage = 1;
      this.loadOrderList(true);
    },

    // 上拉加载更多
    loadMoreOrders() {
      if (!this.noMoreData && !this.loading && !this.refreshing) {
        this.currentPage++;
        this.loadingMore = true;
        this.loadOrderList();
      }
    },

    // 格式化订单状态
    getStatusText(status) {
      const statusMap = {
        '-1': '订单状态',
        '10': '已接单',
        '20': '派单待确认',
        '30': '拒绝接单',
        '40': '已派单',
        '50': '执行中',
        '60': '开始服务',
        '70': '服务结束',
        '80': '已完成',
        '90': '已评价',
        '99': '已取消'
      };
      return statusMap[status] || '未知状态';
    },

    // 格式化订单类型
    getTypeText(type) {
      return type === 1 ? '单次' : '套餐';
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '未知时间';
      try {
        const date = new Date(timeStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      } catch (e) {
        return timeStr;
      }
    },

    // 格式化服务时间
    formatServiceTime(dateStr, hourStr) {
      if (!dateStr) return '未安排';
      try {
        const date = new Date(dateStr);
        const dateFormat = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

        // 优先使用hourStr，如果没有则从dateStr中提取时间
        if (hourStr) {
          return `${dateFormat} ${hourStr}`;
        } else if (dateStr.includes(' ') || dateStr.includes('T')) {
          // 如果dateStr包含时间信息，提取并显示
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          return `${dateFormat} ${hours}:${minutes}`;
        }
        return dateFormat;
      } catch (e) {
        console.error('格式化服务时间失败:', e);
        return dateStr;
      }
    },
    contactCustomer(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
      });
    },

    // 复制订单信息
    copyOrderInfo(order) {
      console.log('复制订单信息:', order);

      if (!order) {
        uni.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      // 格式化服务时间为 YYYY-MM-DD HH:mm:ss 格式
      const formatServiceTime = (serviceDate, serviceHour) => {
        if (!serviceDate) return '未知时间';

        try {
          // 处理服务日期
          let formattedDate = '';
          if (serviceDate.includes('-')) {
            // 如果已经是 YYYY-MM-DD 格式
            formattedDate = serviceDate;
          } else {
            // 如果是其他格式，尝试转换
            const date = new Date(serviceDate);
            if (!isNaN(date.getTime())) {
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              formattedDate = `${year}-${month}-${day}`;
            } else {
              formattedDate = serviceDate;
            }
          }

          // 处理服务时间
          let formattedTime = '00:00:00';
          if (serviceHour) {
            if (serviceHour.includes(':')) {
              // 如果已经包含冒号，检查是否需要补充秒
              formattedTime = serviceHour.includes(':00:') ? serviceHour : serviceHour + ':00';
            } else {
              // 如果只是小时数，补充分钟和秒
              formattedTime = serviceHour + ':00:00';
            }
          }

          return `${formattedDate} ${formattedTime}`;
        } catch (error) {
          console.error('格式化服务时间失败:', error);
          return `${serviceDate || '未知日期'} ${serviceHour || '未知时间'}`;
        }
      };

      // 组装复制内容
      const copyContent = `服务产品：${order.product_name || '未知产品'}
服务时间：${formatServiceTime(order.service_date, order.service_hour)}
客户信息：${order.customer_name || '未知客户'}（${order.customer_mobile || '暂无电话'}）
服务地址：${order.service_address || '未知地址'}`;

      console.log('准备复制的内容:', copyContent);

      uni.setClipboardData({
        data: copyContent,
        success: () => {
          uni.showToast({
            title: '订单信息已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    closeOrder(orderId) {
      // 显示关闭订单确认弹窗
      this.currentOrderId = orderId;
      this.showCloseOrderPopup = true;
    },
    cancelCloseOrder() {
      this.showCloseOrderPopup = false;
      this.currentOrderId = '';
    },
    confirmCloseOrder() {
      if (!this.currentOrderId) {
        return;
      }

      // 这里可以添加关闭订单的API调用

      // 模拟关闭订单成功
      uni.showToast({
        title: '订单已关闭',
        icon: 'success',
      });

      this.showCloseOrderPopup = false;
      this.currentOrderId = '';

      // 刷新订单列表（实际项目中可能需要调用API重新获取订单列表）
      // this.loadOrderList();
    },
    viewSchedule(orderId) {
      // 跳转到预约记录页面
      uni.navigateTo({
        url: `/pages-dispatch/appointment-record?id=${orderId}`,
      });
    },

    createNewAppointment(orderId) {
      uni.navigateTo({
        url: `/pages-dispatch/new-appointment?id=${orderId}`,
      });
    },
    goToOrderDetail(order) {
      // 传递订单号而不是订单ID
      const orderNumber = order.order_number || order.id;
      uni.navigateTo({
        url: `/pages-dispatch/order-detail-new?orderNumber=${orderNumber}`,
      });
    },

    // 派单操作
    async dispatchOrder(order) {
      try {
        // 先检查订单是否已共享到家政人广场
        uni.showLoading({
          title: '检查中...'
        });

        const sharedStatus = await this.checkOrderSharedStatus(order.id);
        uni.hideLoading();

        if (sharedStatus && sharedStatus.is_shared) {
          // 订单已共享，显示确认弹窗
          const statusText = sharedStatus.demand_status_name;
          const commissionText = sharedStatus.commission_amount > 0 ? `，佣金金额：${sharedStatus.commission_amount}元` : '';
          const grabInfo = sharedStatus.grab_store_name ? `，已被【${sharedStatus.grab_store_name}】抢单` : '';

          uni.showModal({
            title: '订单已共享到家政人广场',
            content: `该订单当前状态：${statusText}${commissionText}${grabInfo}。\n\n确定要取消共享并进行派单吗？取消后其他门店将无法继续接单。`,
            success: async (res) => {
              if (res.confirm) {
                // 用户确认取消共享
                try {
                  uni.showLoading({
                    title: '取消共享中...'
                  });

                  const expireResult = await this.expireSharedOrder({
                    order_id: order.id
                  });

                  uni.hideLoading();

                  if (expireResult && expireResult.success !== false) {
                    uni.showToast({
                      title: '已取消共享，正在跳转派单页面',
                      icon: 'success',
                      duration: 1500
                    });

                    // 延迟跳转到派单页面
                    setTimeout(() => {
                      uni.navigateTo({
                        url: `/pages-dispatch/staff-select?orderNumber=${order.order_number}`,
                        fail: (err) => {
                          console.error('跳转派单页面失败:', err);
                          uni.showToast({
                            title: '跳转失败，请重试',
                            icon: 'none'
                          });
                        }
                      });
                    }, 1500);

                    // 刷新订单列表
                    setTimeout(() => {
                      this.loadOrderList(true);
                    }, 500);
                  } else {
                    uni.showToast({
                      title: expireResult.msg || '取消共享失败，请重试',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                } catch (error) {
                  uni.hideLoading();
                  console.error('取消共享失败:', error);
                  uni.showToast({
                    title: error.msg || error.message || '取消共享失败，请重试',
                    icon: 'none',
                    duration: 2000
                  });
                }
              }
              // 如果用户点击取消，什么都不做
            }
          });
        } else {
          // 订单未共享，直接跳转到派单页面
          uni.navigateTo({
            url: `/pages-dispatch/staff-select?orderNumber=${order.order_number}`,
            fail: (err) => {
              console.error('跳转派单页面失败:', err);
              uni.showToast({
                title: '跳转失败，请重试',
                icon: 'none'
              });
            }
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('检查订单共享状态失败:', error);
        // 如果检查失败，直接跳转到派单页面
        uni.navigateTo({
          url: `/pages-dispatch/staff-select?orderNumber=${order.order_number}`,
          fail: (err) => {
            console.error('跳转派单页面失败:', err);
            uni.showToast({
              title: '跳转失败，请重试',
              icon: 'none'
            });
          }
        });
      }
    },

    // 检查订单共享状态API调用
    async checkOrderSharedStatus(orderId) {
      return await checkOrderSharedStatus(orderId);
    },

    // 取消共享订单API调用
    async expireSharedOrder(data) {
      return await expireSharedOrder(data);
    },

    // 批量检查订单的共享状态
    async checkOrdersSharedStatus() {
      try {
        // 只检查可能共享的订单（到家订单且状态为已接单）
        const shareableOrders = this.orderList.filter(order =>
          order.service_type === 1 && order.order_status === 10
        );

        if (shareableOrders.length === 0) {
          return;
        }

        // 并发检查所有订单的共享状态
        const checkPromises = shareableOrders.map(async (order) => {
          try {
            const sharedStatus = await this.checkOrderSharedStatus(order.id);
            return {
              orderId: order.id,
              sharedStatus: sharedStatus
            };
          } catch (error) {
            console.error(`检查订单${order.id}共享状态失败:`, error);
            return {
              orderId: order.id,
              sharedStatus: null
            };
          }
        });

        const results = await Promise.all(checkPromises);

        // 更新订单列表中的共享状态
        results.forEach(result => {
          const orderIndex = this.orderList.findIndex(order => order.id === result.orderId);
          if (orderIndex !== -1) {
            this.$set(this.orderList[orderIndex], 'shared_status', result.sharedStatus);
          }
        });

      } catch (error) {
        console.error('批量检查订单共享状态失败:', error);
      }
    },

    // 确认派单操作
    confirmDispatch(order) {
      // TODO: 调用确认派单接口，将状态从20改为40
      uni.showModal({
        title: '确认派单',
        content: '确认将此订单正式派单给服务人员？',
        success: (res) => {
          if (res.confirm) {
            // TODO: 这里需要调用确认派单的API
            console.log('用户确认派单');
            uni.showToast({
              title: '派单确认成功',
              icon: 'success'
            });
            // 刷新订单列表
            this.loadOrderList(true);
          }
        }
      });
    },

    // 重派操作
    redispatchOrder(order) {
      console.log('重派操作', order);
      // 跳转到员工选择页面进行重新派单，使用相同的派单接口
      uni.navigateTo({
        url: `/pages-dispatch/staff-select?orderNumber=${order.order_number}&redispatch=true`,
        fail: (err) => {
          console.error('跳转重派页面失败:', err);
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none'
          });
        }
      });
    },

    // 处理付款
    handlePayment(order) {
      this.currentPaymentOrder = order;
      this.showPaymentModal = true;
    },

    // 支付弹窗关闭
    handlePaymentModalClose() {
      this.showPaymentModal = false;
      this.currentPaymentOrder = null;
    },

    // 支付方式选择
    handlePaymentMethodSelect(method) {
      this.showPaymentModal = false;

      const orderToProcess = this.currentPaymentOrder;

      if (method.key === 'store_deduct') {
        // 门店代扣：执行原有的扣费逻辑
        this.processStoreDeductPayment(orderToProcess);
        this.currentPaymentOrder = null;
      } else if (method.key === 'cash_pay') {
        // 现金支付：执行现金支付逻辑
        this.processCashPayment(orderToProcess);
        this.currentPaymentOrder = null;
      } else if (method.key === 'balance_pay') {
        // 余额支付：执行余额支付逻辑
        this.processBalancePayment(orderToProcess);
        this.currentPaymentOrder = null;
      } else if (method.key === 'qrcode_pay') {
        // 扫码支付：执行扫码支付逻辑（不立即清空currentPaymentOrder，等扫码支付完成后再清空）
        this.processQrcodePayment(orderToProcess);
      } else {
        this.currentPaymentOrder = null;
      }
    },

    // 执行门店代扣支付（原processPayment方法重命名）
    async processStoreDeductPayment(order) {
      // 先显示确认对话框
      uni.showModal({
        title: '确认门店代扣',
        content: `确认对订单 ${order.order_number} 进行门店代扣？\n金额：¥${order.total_pay_actual || order.pay_actual || 0}`,
        success: async (res) => {
          if (res.confirm) {
            await this.performStoreDeductPayment(order);
          }
        }
      });
    },

    // 执行扣费操作
    async performStoreDeductPayment(order) {
      try {
        uni.showLoading({
          title: '扣费中...'
        });

        // 调用手动余额扣费接口
        const response = await this.$post('/api/v1/order/manual-balance-payment', {
          order_number: order.order_number
        }, { contentType: 'application/json' });

        uni.hideLoading();

        if (response) {
          uni.showToast({
            title: '扣费成功',
            icon: 'success'
          });

          // 更新订单支付状态
          order.pay_status = 1;

          // 触发订单列表刷新
          this.$emit('refresh');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('扣费失败:', error);
        uni.showToast({
          title: error.msg || error.message || '扣费失败，请重试',
          icon: 'none'
        });
      }
    },

    // 现金支付操作
    async processCashPayment(order) {
      // 先显示确认对话框
      uni.showModal({
        title: '确认现金支付',
        content: `确认订单 ${order.order_number} 已收到现金？\n金额：¥${order.total_pay_actual || order.pay_actual || 0}`,
        success: async (res) => {
          if (res.confirm) {
            await this.performCashPayment(order);
          }
        }
      });
    },

    // 执行现金支付操作
    async performCashPayment(order) {
      try {
        uni.showLoading({
          title: '处理中...'
        });

        // 调用现金支付接口
        const response = await this.$post('/api/v1/order/cash-payment', {
          order_number: order.order_number
        }, { contentType: 'application/json' });

        uni.hideLoading();

        if (response) {
          uni.showToast({
            title: '现金支付成功',
            icon: 'success'
          });

          // 更新订单支付状态
          order.pay_status = 1;

          // 触发订单列表刷新
          this.$emit('refresh');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('现金支付失败:', error);
        uni.showToast({
          title: error.msg || error.message || '现金支付失败，请重试',
          icon: 'none'
        });
      }
    },

    // 余额支付操作
    async processBalancePayment(order) {
      // 先显示确认对话框
      uni.showModal({
        title: '确认余额支付',
        content: `确认使用客户余额支付订单 ${order.order_number}？\n金额：¥${order.total_pay_actual || order.pay_actual || 0}`,
        success: async (res) => {
          if (res.confirm) {
            await this.performBalancePayment(order);
          }
        }
      });
    },

    // 执行余额支付操作
    async performBalancePayment(order) {
      try {
        uni.showLoading({
          title: '处理中...'
        });

        // 调用余额支付接口
        const response = await this.$post('/api/v1/order/balance-payment', {
          order_number: order.order_number
        }, { contentType: 'application/json' });

        uni.hideLoading();

        if (response) {
          uni.showToast({
            title: '余额支付成功',
            icon: 'success'
          });

          // 更新订单支付状态
          order.pay_status = 1;

          // 触发订单列表刷新
          this.$emit('refresh');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('余额支付失败:', error);
        uni.showToast({
          title: error.msg || error.message || '余额支付失败，请重试',
          icon: 'none'
        });
      }
    },

    // 取消订单操作
    cancelOrder(order) {
      console.log('取消订单操作', order);
      uni.showModal({
        title: '取消订单',
        content: `确认要取消订单 ${order.order_number} 吗？此操作不可撤销。`,
        confirmText: '确认取消',
        confirmColor: '#fa3534',
        success: (res) => {
          if (res.confirm) {
            this.performCancelOrder(order);
          }
        }
      });
    },

    // 执行取消订单
    async performCancelOrder(order) {
      try {
        console.log('执行取消订单:', order.order_number);

        // 显示加载状态
        uni.showLoading({
          title: '取消中...'
        });

        // 调用取消订单API
        await cancelOrder(order.order_number);

        uni.hideLoading();
        uni.showToast({
          title: '订单已取消',
          icon: 'success'
        });

        // 刷新订单列表
        this.loadOrderList(true);

      } catch (error) {
        uni.hideLoading();
        console.error('取消订单失败:', error);

        // 根据错误类型显示不同的提示信息
        let errorMessage = '取消失败，请重试';
        if (error && error.msg) {
          errorMessage = error.msg;
        } else if (error && error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    },

    // ==================== 改时/改价功能相关方法 ====================

    // 显示改时/改价弹窗
    openEditOrderPopup(order) {
      console.log('打开改时改价弹窗，订单信息:', order);
      
      this.currentEditOrder = order;
      
      // 确保金额有值，默认为订单金额
      const currentAmount = order.total_pay_actual || order.pay_actual || '0.00';
      this.newAmount = String(currentAmount);
      console.log('设置新金额:', this.newAmount);

      // 解析当前服务时间 - 优先使用service_hour，如果没有则从service_date中解析
      if (order.service_date) {
        try {
          // 如果service_date包含时间信息，直接解析
          const serviceDate = new Date(order.service_date);
          this.newServiceDate = this.formatDate(serviceDate);

          // 优先使用service_hour字段，如果没有则从service_date中提取小时
          if (order.service_hour) {
            // service_hour格式可能是 "08:00" 或 "08"
            const hourPart = order.service_hour.split(':')[0];
            this.newServiceHour = hourPart.padStart(2, '0');
          } else {
            // 从service_date中提取小时
            this.newServiceHour = String(serviceDate.getHours()).padStart(2, '0');
          }
          
          console.log('设置新服务日期:', this.newServiceDate);
          console.log('设置新服务时间:', this.newServiceHour);
        } catch (e) {
          console.error('解析服务时间失败:', e);
          this.newServiceDate = '';
          this.newServiceHour = '';
        }
      } else {
        this.newServiceDate = '';
        this.newServiceHour = '';
      }

      this.showEditOrderPopup = true;
      
      // 使用 $nextTick 确保DOM更新后数据能正确显示
      this.$nextTick(() => {
        console.log('弹窗显示后的数据状态:', {
          newAmount: this.newAmount,
          newServiceDate: this.newServiceDate,
          newServiceHour: this.newServiceHour,
          showEditOrderPopup: this.showEditOrderPopup
        });
        
        // 强制触发页面更新
        this.$forceUpdate();
      });
    },

    // 关闭改时/改价弹窗
    closeEditOrderPopup() {
      this.showEditOrderPopup = false;
      this.currentEditOrder = {};
      this.newAmount = '';
      this.newServiceDate = '';
      this.newServiceHour = '';
    },

    // 验证金额输入
    validateAmount() {
      // 移除非数字和小数点的字符
      this.newAmount = this.newAmount.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = this.newAmount.split('.');
      if (parts.length > 2) {
        this.newAmount = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数点后最多两位
      if (parts[1] && parts[1].length > 2) {
        this.newAmount = parts[0] + '.' + parts[1].substring(0, 2);
      }
    },

    // 金额输入框获得焦点
    onAmountFocus() {
      console.log('金额输入框获得焦点');
    },

    // 金额输入框失去焦点
    onAmountBlur() {
      console.log('金额输入框失去焦点');
      // 可以在这里进行格式化，比如保证两位小数
      if (this.newAmount && !isNaN(parseFloat(this.newAmount))) {
        this.newAmount = parseFloat(this.newAmount).toFixed(2);
      }
    },

    // 确认修改订单信息（价格和/或时间）
    async confirmEditOrder() {
      // 检查是否有任何修改
      const hasAmountChange = this.newAmount && String(this.newAmount).trim() !== '' &&
                             parseFloat(this.newAmount) !== parseFloat(this.currentEditOrder.total_pay_actual || 0);
      const hasTimeChange = this.newServiceDate && this.newServiceHour;

      // 检查订单状态和修改权限
      if (hasTimeChange && (this.currentEditOrder.order_status === 80 || this.currentEditOrder.order_status === 99)) {
        uni.showToast({
          title: '当前订单状态不允许修改服务时间',
          icon: 'none'
        });
        return;
      }

      if (!hasAmountChange && !hasTimeChange) {
        uni.showToast({
          title: '请至少修改一项内容',
          icon: 'none'
        });
        return;
      }

      // 验证金额（如果有修改）
      if (hasAmountChange) {
        const amount = parseFloat(this.newAmount);
        if (isNaN(amount) || amount <= 0) {
          uni.showToast({
            title: '请输入有效的金额',
            icon: 'none'
          });
          return;
        }

        if (amount > 99999.99) {
          uni.showToast({
            title: '金额不能超过99999.99',
            icon: 'none'
          });
          return;
        }
      }

      // 验证时间（如果有修改）
      if (hasTimeChange) {
        const newServiceDateTime = `${this.newServiceDate} ${this.newServiceHour}:00:00`;
        const serviceDate = new Date(newServiceDateTime);
        const now = new Date();

        // 如果是当天，需要确保时间在当前时间之后
        // 如果是未来日期，则不需要检查具体时间
        const selectedDate = new Date(this.newServiceDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        selectedDate.setHours(0, 0, 0, 0);

        if (selectedDate.getTime() === today.getTime()) {
          // 当天的情况，需要检查具体时间
          if (serviceDate <= now) {
            uni.showToast({
              title: '服务时间必须为当前时间之后',
              icon: 'none'
            });
            return;
          }
        } else if (selectedDate < today) {
          // 过去日期不允许
          uni.showToast({
            title: '不能选择过去的日期',
            icon: 'none'
          });
          return;
        }
      }

      try {
        uni.showLoading({
          title: '修改中...'
        });

        // 同时修改金额和时间
        if (hasAmountChange && hasTimeChange) {
          const newServiceDateTime = `${this.newServiceDate} ${this.newServiceHour}:00:00`;

          await Promise.all([
            updateOrderAmount({
              order_number: this.currentEditOrder.order_number,
              new_amount: parseFloat(this.newAmount)
            }),
            updateOrderTime({
              order_number: this.currentEditOrder.order_number,
              new_service_date: newServiceDateTime
            })
          ]);

          uni.showToast({
            title: '订单信息修改成功',
            icon: 'success'
          });
        }
        // 只修改金额
        else if (hasAmountChange) {
          await updateOrderAmount({
            order_number: this.currentEditOrder.order_number,
            new_amount: parseFloat(this.newAmount)
          });

          uni.showToast({
            title: '订单金额修改成功',
            icon: 'success'
          });
        }
        // 只修改时间
        else if (hasTimeChange) {
          const newServiceDateTime = `${this.newServiceDate} ${this.newServiceHour}:00:00`;

          await updateOrderTime({
            order_number: this.currentEditOrder.order_number,
            new_service_date: newServiceDateTime
          });

          uni.showToast({
            title: '服务时间修改成功',
            icon: 'success'
          });
        }

        uni.hideLoading();
        this.closeEditOrderPopup();
        this.loadOrderList(true);

      } catch (error) {
        uni.hideLoading();
        console.error('修改订单信息失败:', error);

        let errorMessage = '修改失败，请重试';
        if (error && error.msg) {
          errorMessage = error.msg;
        } else if (error && error.message) {
          errorMessage = error.message;
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none'
        });
      }
    },



    // 显示服务日期选择器
    showServiceDatePicker() {
      console.log('点击服务日期选择器');
      // 设置最小日期为今天
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 设置为今天的开始时间
      this.minServiceDate = today.getTime();

      // 如果已有选择的日期，使用它；否则使用今天
      if (this.newServiceDate) {
        this.tempServiceDate = new Date(this.newServiceDate).getTime();
      } else {
        this.tempServiceDate = today.getTime();
      }

      this.showServiceDatePickerModal = true;
      console.log('服务日期选择器应该显示了:', this.showServiceDatePickerModal);
    },

    // 显示服务小时选择器
    showServiceHourPicker() {
      console.log('点击服务时间选择器');
      this.showServiceHourPickerModal = true;
      console.log('服务时间选择器应该显示了:', this.showServiceHourPickerModal);
    },

    // 确认服务日期
    confirmServiceDate(e) {
      this.newServiceDate = this.formatDate(new Date(e.value));
      this.showServiceDatePickerModal = false;
    },

    // 确认服务小时
    confirmServiceHour(e) {
      const selectedHour = e.value[0];
      this.newServiceHour = selectedHour.value;
      this.showServiceHourPickerModal = false;
    },

    // ==================== 扫码支付相关方法 ====================

    // 扫码支付操作
    async processQrcodePayment(order) {
      try {
        uni.showLoading({
          title: '创建支付订单...'
        });

        // 调用订单扫码支付接口
        const response = await this.$post('/api/v1/order/qrcode-payment', {
          order_number: order.order_number
        }, { contentType: 'application/json' });

        uni.hideLoading();

        if (response && response.payUrl) {
          // 保存订单信息和支付链接
          this.currentOrderNumber = response.order_number || order.order_number;
          this.currentPaymentOrderNumber = response.payment_order_number; // 支付订单号
          this.currentOrderAmount = response.amount || order.total_pay_actual || order.pay_actual || 0;
          this.payUrl = response.payUrl;

          // 输出payUrl到控制台
          console.log('订单扫码支付URL:', response.payUrl);

          // 显示二维码弹窗
          this.openQrcodeModal();

          // 开始轮询支付状态（使用支付订单号）
          this.startPaymentStatusPolling(this.currentPaymentOrderNumber);
        } else {
          uni.showToast({
            title: response.msg || '创建扫码支付订单失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('扫码支付失败:', error);
        uni.showToast({
          title: error.msg || error.message || '扫码支付失败，请重试',
          icon: 'none'
        });
      }
    },

    // 显示二维码弹窗
    openQrcodeModal() {
      this.showQrcodeModal = true;
      this.$nextTick(() => {
        this.generateQRCode();
      });
    },

    // 处理完成支付按钮点击
    async handlePaymentComplete() {
      if (!this.currentPaymentOrderNumber) {
        this.closeQrcodeModal();
        return;
      }

      try {
        uni.showLoading({
          title: '查询支付结果...'
        });

        // 主动查询一次支付状态
        const result = await this.$get(`/api/v1/payment/order/status/${this.currentPaymentOrderNumber}`);

        uni.hideLoading();

        if (result && result.status === 'success') {
          // 支付成功
          this.stopPaymentStatusPolling();
          this.showQrcodeModal = false;
          this.currentPaymentOrder = null;

          uni.showToast({
            title: '支付成功！',
            icon: 'success',
            duration: 2000
          });

          // 刷新订单列表
          setTimeout(() => {
            this.loadOrderList(true);
          }, 1000);

        } else if (result && result.status === 'failed') {
          // 支付失败
          this.stopPaymentStatusPolling();
          this.showQrcodeModal = false;
          this.currentPaymentOrder = null;

          uni.showToast({
            title: '支付失败',
            icon: 'none',
            duration: 2000
          });

        } else {
          // 支付还在处理中
          uni.showToast({
            title: '支付还在处理中，请稍后再试',
            icon: 'none',
            duration: 2000
          });
        }

      } catch (error) {
        uni.hideLoading();
        console.error('查询支付结果失败:', error);

        uni.showToast({
          title: '查询支付结果失败，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 关闭二维码弹窗
    closeQrcodeModal() {
      this.showQrcodeModal = false;
      this.stopPaymentStatusPolling();
      this.payUrl = '';
      this.currentOrderNumber = '';
      this.currentPaymentOrderNumber = '';
      this.currentOrderAmount = 0;
      this.currentPaymentOrder = null;
    },

    // 生成二维码
    generateQRCode() {
      if (!this.payUrl) {
        console.error('支付链接为空，无法生成二维码');
        uni.showToast({
          title: '支付链接无效',
          icon: 'none'
        });
        return;
      }

      try {
        // 导入增强的二维码生成器
        const qrCodeGenerator = require('@/utils/uqrcode.js').default;

        // 重置API索引，确保使用最稳定的API
        qrCodeGenerator.resetApiIndex();

        // 首先尝试使用image模式显示二维码
        this.qrcodeImageUrl = qrCodeGenerator.getQRCodeUrl(this.payUrl, this.qrcodeSize);
        this.useCanvasMode = false;

        if (this.qrcodeImageUrl) {
          console.log('二维码URL生成成功:', this.qrcodeImageUrl);
        } else {
          console.warn('二维码URL生成失败，切换到canvas模式');
          this.fallbackToCanvasMode();
        }

      } catch (error) {
        console.error('生成二维码失败:', error);
        uni.showToast({
          title: '二维码生成失败，正在重试',
          icon: 'none'
        });
        this.fallbackToCanvasMode();
      }
    },

    // 回退到canvas模式
    fallbackToCanvasMode() {
      console.log('回退到canvas模式，开始使用增强的canvas生成器');
      this.useCanvasMode = true;
      this.qrcodeImageUrl = '';

      try {
        // 使用增强的canvas模式生成二维码
        const qrCodeGenerator = require('@/utils/uqrcode.js').default;
        
        // 显示加载提示
        uni.showLoading({
          title: '生成二维码中...',
          mask: true
        });

        qrCodeGenerator.make({
          canvasId: 'qrcode-canvas',
          componentInstance: this,
          text: this.payUrl,
          size: this.qrcodeSize,
          margin: 10,
          backgroundColor: '#ffffff',
          foregroundColor: '#000000',
          success: (result) => {
            uni.hideLoading();
            console.log('canvas二维码生成成功');
            if (result && result.fallback) {
              uni.showToast({
                title: '二维码生成异常，请复制链接',
                icon: 'none',
                duration: 3000
              });
            }
          },
          fail: (err) => {
            uni.hideLoading();
            console.error('canvas二维码生成失败:', err);
            this.drawQRCodeFallback();
          }
        });
      } catch (error) {
        uni.hideLoading();
        console.error('canvas模式初始化失败:', error);
        this.drawQRCodeFallback();
      }
    },

    // 处理二维码图片加载错误
    handleQrcodeImageError() {
      console.log('二维码图片加载失败，尝试使用备用API重新生成');
      
      try {
        const qrCodeGenerator = require('@/utils/uqrcode.js').default;
        
        // 尝试下一个API
        const nextUrl = qrCodeGenerator.getQRCodeUrl(this.payUrl, this.qrcodeSize);
        
        if (nextUrl && nextUrl !== this.qrcodeImageUrl) {
          console.log('尝试使用备用API:', nextUrl);
          this.qrcodeImageUrl = nextUrl;
          return;
        }
      } catch (error) {
        console.error('备用API尝试失败:', error);
      }

      // 如果所有image模式都失败，切换到canvas模式
      console.log('所有图片模式失败，切换到canvas模式');
      this.fallbackToCanvasMode();
    },

    // 二维码生成失败时的备用方案
    drawQRCodeFallback() {
      console.error('所有二维码生成尝试均失败，显示提示');
      
      uni.showModal({
        title: '二维码生成失败',
        content: '网络连接不稳定，无法生成二维码。您可以:\n\n1. 点击"复制支付链接"按钮\n2. 刷新页面重试\n3. 检查网络连接',
        showCancel: true,
        confirmText: '复制链接',
        cancelText: '重试',
        success: (res) => {
          if (res.confirm) {
            // 用户选择复制链接
            this.copyPayUrl();
          } else if (res.cancel) {
            // 用户选择重试
            setTimeout(() => {
              this.generateQRCode();
            }, 1000);
          }
        }
      });
    },

    // 复制支付链接
    copyPayUrl() {
      if (!this.payUrl) {
        uni.showToast({
          title: '支付链接为空',
          icon: 'none'
        });
        return;
      }

      uni.setClipboardData({
        data: this.payUrl,
        success: () => {
          uni.showToast({
            title: '支付链接已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 开始轮询支付状态
    startPaymentStatusPolling(orderNumber) {
      this.pollingCount = 0;
      this.pollingTimer = setInterval(() => {
        this.checkPaymentStatus(orderNumber);
      }, 2000); // 每2秒轮询一次，提高检测频率

      // 立即检查一次
      setTimeout(() => {
        this.checkPaymentStatus(orderNumber);
      }, 1000); // 1秒后首次检查
    },

    // 停止轮询支付状态
    stopPaymentStatusPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
      this.pollingCount = 0;
    },

    // 检查支付状态
    async checkPaymentStatus(orderNumber) {
      try {
        this.pollingCount++;

        // 查询支付状态
        const result = await this.$get(`/api/v1/payment/order/status/${orderNumber}`);

        if (!result) {
          throw new Error('查询API返回空结果');
        }

        const status = result.status;

        if (status === 'success') {
          // 支付成功
          this.stopPaymentStatusPolling();

          // 关闭二维码弹窗
          this.showQrcodeModal = false;

          // 清空支付相关数据
          this.currentPaymentOrder = null;

          // 隐藏所有Toast
          uni.hideToast();

          // 显示支付成功提示
          uni.showToast({
            title: '支付成功！',
            icon: 'success',
            duration: 2000
          });

          // 刷新订单列表
          setTimeout(() => {
            this.loadOrderList(true);
          }, 1000);

        } else if (status === 'failed') {
          // 支付失败
          this.stopPaymentStatusPolling();

          // 关闭二维码弹窗
          this.showQrcodeModal = false;

          // 清空支付相关数据
          this.currentPaymentOrder = null;

          // 隐藏所有Toast
          uni.hideToast();

          uni.showToast({
            title: '支付失败',
            icon: 'none',
            duration: 2000
          });

        } else if (this.pollingCount >= this.maxPollingCount) {
          // 超时停止轮询
          this.stopPaymentStatusPolling();
          uni.showToast({
            title: '支付超时，请手动刷新订单状态',
            icon: 'none',
            duration: 3000
          });
        }

      } catch (error) {
        console.error('查询支付状态失败:', error);

        if (this.pollingCount >= this.maxPollingCount) {
          this.stopPaymentStatusPolling();
          uni.showToast({
            title: '查询支付状态失败，请手动刷新',
            icon: 'none'
          });
        }
      }
    },

    // 判断订单是否可以共享到家政广场
    canShareOrder(order) {
      // 只有到家订单且状态为已接单(10)的订单可以共享
      return order.service_type === 1 && // 到家服务
             order.order_status === 10; // 已接单状态
    },

    // 判断订单是否已共享
    isOrderShared(order) {
      return order.shared_status && order.shared_status.is_shared;
    },

    // 获取共享按钮文案
    getShareButtonText(order) {
      if (this.isOrderShared(order)) {
        const sharedStatus = order.shared_status;
        if (sharedStatus.demand_status === 1) {
          return '已共享（待抢单）';
        } else if (sharedStatus.demand_status === 2) {
          return `已共享（已被${sharedStatus.grab_store_name}抢单）`;
        } else {
          return '已共享到广场';
        }
      }
      return '共享到家政广场';
    },

    // 判断是否可以调整佣金
    canAdjustCommission(order) {
      // 只有已共享且状态为待抢单的订单才能调整佣金
      return this.isOrderShared(order) &&
             order.shared_status &&
             order.shared_status.demand_status === 1; // 待抢单状态
    },

    // 共享订单到家政广场
    async shareOrderToSquare(order) {
      try {
        // 先检查订单是否已共享到家政人广场
        uni.showLoading({
          title: '检查中...'
        });

        const sharedStatus = await this.checkOrderSharedStatus(order.id);
        uni.hideLoading();

        if (sharedStatus && sharedStatus.is_shared) {
          // 订单已共享，显示状态信息
          const statusText = sharedStatus.demand_status_name;
          const commissionText = sharedStatus.commission_amount > 0 ? `，到手金额：${sharedStatus.commission_amount}元` : '';
          const grabInfo = sharedStatus.grab_store_name ? `，已被【${sharedStatus.grab_store_name}】抢单` : '';

          uni.showModal({
            title: '订单已共享到家政人广场',
            content: `该订单当前状态：${statusText}${commissionText}${grabInfo}。\n\n如需取消共享，请点击"去派单"按钮进行操作。`,
            showCancel: false,
            confirmText: '知道了'
          });
          return;
        }

        // 订单未共享，显示共享设置弹窗
        this.openShareSettingModal(order);
      } catch (error) {
        console.error('共享订单操作失败:', error);
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },

    // 显示共享设置弹窗
    openShareSettingModal(order) {
      this.currentShareOrder = order;
      this.shareCommissionAmount = '';
      this.shareExpireHours = 48; // 重置为默认值
      this.shareCustomHours = '';
      this.showShareSettingModal = true;
    },

    // 关闭共享设置弹窗
    closeShareSettingModal() {
      this.showShareSettingModal = false;
      this.currentShareOrder = null;
      this.shareCommissionAmount = '';
      this.shareExpireHours = 48;
      this.shareCustomHours = '';
    },

    // 验证佣金输入
    validateShareCommissionInput(e) {
      let value = e.detail.value;
      // 只允许数字和一个小数点
      value = value.replace(/[^\d.]/g, '');
      // 只允许一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      // 限制小数点后两位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
      this.shareCommissionAmount = value;
    },

    // 选择过期时间
    selectShareExpireTime(value) {
      this.shareExpireHours = value;
      if (value !== 'custom') {
        this.shareCustomHours = '';
      }
    },

    // 验证自定义时间
    validateShareCustomTime(e) {
      let value = e.detail.value;
      // 只允许数字
      value = value.replace(/[^\d]/g, '');
      // 限制范围 1-168
      if (value && (parseInt(value) < 1 || parseInt(value) > 168)) {
        if (parseInt(value) < 1) value = '1';
        if (parseInt(value) > 168) value = '168';
      }
      this.shareCustomHours = value;
    },

    // 确认共享订单
    async confirmShareOrder() {
      if (!this.currentShareOrder) return;

      const commission = parseFloat(this.shareCommissionAmount);

      // 验证佣金金额
      if (isNaN(commission) || commission <= 0) {
        uni.showToast({
          title: '请输入有效的到手金额',
          icon: 'none'
        });
        return;
      }

      if (commission > parseFloat(this.currentShareOrder.pay_actual || 0)) {
        uni.showToast({
          title: '到手金额不能超过订单金额',
          icon: 'none'
        });
        return;
      }

      // 获取最终的过期小时数
      let finalExpireHours = this.shareExpireHours;
      if (this.shareExpireHours === 'custom') {
        finalExpireHours = parseInt(this.shareCustomHours) || 1;
        if (finalExpireHours < 1 || finalExpireHours > 168) {
          uni.showToast({
            title: '自定义时间范围：1-168小时',
            icon: 'none'
          });
          return;
        }
      }

      // 获取过期时间显示文本
      const expireText = this.shareExpireHours === 'custom'
        ? `${this.shareCustomHours}小时`
        : this.shareTimeOptions.find(opt => opt.value === this.shareExpireHours)?.label || '';

      // 最终确认弹窗
      uni.showModal({
        title: '确认共享',
        content: `确定要将订单共享到家政广场吗？\n\n订单号：${this.currentShareOrder.order_number}\n到手金额：${commission}元\n过期时间：${expireText}\n\n共享后其他门店可以接单并获得佣金。`,
        success: async (res) => {
          if (res.confirm) {
            await this.executeShareOrder(commission, finalExpireHours);
          }
        }
      });
    },

    // 执行共享订单
    async executeShareOrder(commissionAmount, expireHours) {
      try {
        uni.showLoading({
          title: '正在共享...'
        });

        // 调用共享订单API
        const response = await this.shareOrderToDemandSquare({
          order_id: this.currentShareOrder.id,
          order_number: this.currentShareOrder.order_number,
          commission_amount: commissionAmount,
          expire_hours: expireHours
        });

        uni.hideLoading();

        if (response && response.success !== false) {
          // 关闭弹窗
          this.closeShareSettingModal();

          uni.showToast({
            title: '订单已共享到家政广场',
            icon: 'success',
            duration: 2000
          });

          // 刷新订单列表
          setTimeout(() => {
            this.loadOrderList(true);
          }, 1000);
        } else {
          uni.showToast({
            title: response.msg || '共享失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('共享订单失败:', error);
        uni.hideLoading();

        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    },

    // 显示佣金调整弹窗
    openAdjustCommissionModal(order) {
      if (!this.canAdjustCommission(order)) {
        uni.showToast({
          title: '该订单无法调整到手金额',
          icon: 'none'
        });
        return;
      }

      this.currentAdjustOrder = order;
      this.currentCommissionAmount = parseFloat(order.shared_status.commission_amount || 0);
      this.newCommissionAmount = '';
      this.showAdjustCommissionModal = true;
    },

    // 关闭佣金调整弹窗
    closeAdjustCommissionModal() {
      this.showAdjustCommissionModal = false;
      this.currentAdjustOrder = null;
      this.newCommissionAmount = '';
      this.currentCommissionAmount = 0;
    },

    // 验证新佣金输入
    validateNewCommissionInput(e) {
      let value = e.detail.value;
      // 只允许数字和一个小数点
      value = value.replace(/[^\d.]/g, '');
      // 只允许一个小数点
      const parts = value.split('.');
      if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
      }
      // 限制小数点后两位
      if (parts[1] && parts[1].length > 2) {
        value = parts[0] + '.' + parts[1].substring(0, 2);
      }
      this.newCommissionAmount = value;
    },

    // 确认调整佣金
    async confirmAdjustCommission() {
      if (!this.currentAdjustOrder) return;

      const newCommission = parseFloat(this.newCommissionAmount);

      // 验证新佣金金额
      if (isNaN(newCommission) || newCommission <= 0) {
        uni.showToast({
          title: '请输入有效的到手金额',
          icon: 'none'
        });
        return;
      }

      if (newCommission <= this.currentCommissionAmount) {
        uni.showToast({
          title: `新到手金额必须大于当前到手金额¥${this.currentCommissionAmount}`,
          icon: 'none'
        });
        return;
      }

      if (newCommission > parseFloat(this.currentAdjustOrder.pay_actual || 0)) {
        uni.showToast({
          title: '到手金额不能超过订单金额',
          icon: 'none'
        });
        return;
      }

      // 最终确认弹窗
      uni.showModal({
        title: '确认调整到手金额',
        content: `确定要调整到手金额吗吗？\n\n订单号：${this.currentAdjustOrder.order_number}\n当前到手金额：¥${this.currentCommissionAmount}\n新佣金：¥${newCommission}\n\n调整后将在家政广场中立即生效。`,
        success: async (res) => {
          if (res.confirm) {
            await this.executeAdjustCommission(newCommission);
          }
        }
      });
    },

    // 执行佣金调整
    async executeAdjustCommission(newCommissionAmount) {
      try {
        uni.showLoading({
          title: '正在调整...'
        });

        console.log('开始调整到手金额:', {
          order_id: this.currentAdjustOrder.id,
          order_number: this.currentAdjustOrder.order_number,
          old_commission: this.currentCommissionAmount,
          new_commission: newCommissionAmount
        });

        // 调用佣金调整API
        const response = await this.adjustSharedOrderCommission({
          order_id: this.currentAdjustOrder.id,
          new_commission_amount: newCommissionAmount
        });

        uni.hideLoading();

        console.log('到手金额调整API响应:', response);

        if (response && response.success !== false) {
          // 关闭弹窗
          this.closeAdjustCommissionModal();

          uni.showToast({
            title: '到手金额调整成功',
            icon: 'success',
            duration: 2000
          });

          // 刷新订单列表
          setTimeout(() => {
            this.loadOrderList(true);
          }, 1000);
        } else {
          const errorMsg = response?.msg || response?.message || '调整失败，请重试';
          console.error('到手金额调整失败:', errorMsg);

          uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          });
        }
      } catch (error) {
        console.error('调整到手金额异常:', error);
        uni.hideLoading();

        let errorMessage = '网络错误，请重试';

        // 根据错误类型提供更具体的提示
        if (error.message) {
          if (error.message.includes('timeout')) {
            errorMessage = '请求超时，请检查网络连接';
          } else if (error.message.includes('Network')) {
            errorMessage = '网络连接异常，请检查网络';
          } else if (error.message.includes('500')) {
            errorMessage = '服务器内部错误，请稍后重试';
          } else if (error.message.includes('403')) {
            errorMessage = '权限不足，请重新登录';
          } else {
            errorMessage = error.message;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        });
      }
    },

    // 调用佣金调整API
    async adjustSharedOrderCommission(data) {
      // 调用真实的API接口
      return await adjustSharedOrderCommission(data);
    },

    // 调用共享订单API
    async shareOrderToDemandSquare(data) {
      // 调用真实的API接口
      return await shareOrderToDemandSquare(data);
    },






  },


};
</script>

<style lang="scss" scoped>
.order-area-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.tab-nav {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #333333;

    &.active-tab {
      color: #fdd118;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40rpx;
        height: 4rpx;
        background-color: #fdd118;
      }
    }
  }
}

// 搜索筛选容器
.search-filter-container {
  background-color: #ffffff;
  margin-bottom: 0;
  flex-shrink: 0; // 防止被压缩
}

.search-box {
  padding: 30rpx 30rpx 0;

  .search-input {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    padding: 10rpx 20rpx;

    input {
      flex: 1;
      height: 60rpx;
      font-size: 26rpx;
      margin-left: 10rpx;
    }
  }
}

.filter-options {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 30rpx;
  position: relative;
  z-index: 10;

  .filter-item {
    display: flex;
    align-items: center;
    font-size: 26rpx;
    color: #666666;
    padding: 10rpx 20rpx;

    > text {
      margin-right: 10rpx;
    }
  }
}

// 筛选扩展区域
.filter-extension {
  padding: 0 30rpx 20rpx;

  // 时间筛选
  .time-filter {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20rpx;

    .time-filter-item {
      flex: 1;
      text-align: center;
      padding: 12rpx 8rpx;
      margin: 0 6rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #666666;
      background-color: #f8f8f8;
      transition: all 0.3s ease;

      &.active {
        background-color: #fdd118;
        color: #ffffff;
        font-weight: 500;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // 横向滚动状态筛选
  .horizontal-status-filter {
    .status-scroll {
      white-space: nowrap;

      .status-scroll-content {
        display: flex;
        padding: 0 10rpx;

        .status-filter-item {
          flex-shrink: 0;
          padding: 10rpx 20rpx;
          margin-right: 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          color: #666666;
          background-color: #f8f8f8;
          border: 1rpx solid #e8e8e8;
          transition: all 0.3s ease;
          white-space: nowrap;

          &.active {
            background-color: #fdd118;
            color: #ffffff;
            border-color: #fdd118;
            font-weight: 500;
          }

          &:active {
            transform: scale(0.95);
          }

          &:last-child {
            margin-right: 10rpx;
          }
        }
      }
    }
  }
}



.order-list {
  height: 100%; // 占满父容器高度
  padding: 0 30rpx 40rpx; // 增加底部间距，防止内容被截断

  .order-item {
    background-color: #ffffff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    .order-time {
      display: flex;
      justify-content: space-between;
      font-size: 26rpx;
      color: #666666;
      margin-bottom: 15rpx;

      .order-status {
        display: flex;
        align-items: center;

        .status-tag-container {
          display: flex;
          align-items: center;
          gap: 10rpx;
        }

        .status-tag {
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
          font-weight: 500;

          .status-text {
            color: #ffffff;
          }

          &.waiting-payment {
            background-color: #ff9900;
          }

          &.paid {
            background-color: #3399ff;
          }

          &.dispatch-confirm {
            background-color: #ff9900;
          }

          &.rejected {
            background-color: #ff4444;
          }

          &.dispatched {
            background-color: #722ed1;
          }

          &.in-progress {
            background-color: #ff5500;
          }

          &.service-started {
            background-color: #ff5500;
          }

          &.service-ended {
            background-color: #ff5500;
          }

          &.completed {
            background-color: #00cc66;
          }

          &.reviewed {
            background-color: #00cc66;
          }

          &.cancelled {
            background-color: #999999;
          }
        }

        .order-amount {
          font-size: 24rpx;
          color: #333333;
          font-weight: 600;
        }
      }
    }

    .order-content {
      .order-type {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 15rpx;

        .type-name {
          margin-left: 10rpx;
        }
      }

      .order-detail-item {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #666666;
        margin-bottom: 10rpx;

        .detail-text {
          margin-left: 10rpx;
        }

        .contact-btn {
          margin-left: 20rpx;
          background-color: #fdd118;
          color: #ffffff;
          padding: 4rpx 12rpx;
          border-radius: 4rpx;
          font-size: 24rpx;
        }

        .copy-info-btn {
          margin-left: 20rpx;
          background-color: #ff9800 !important;
          color: #ffffff !important;
          padding: 4rpx 12rpx;
          border-radius: 4rpx;
          font-size: 24rpx;
          display: inline-block;
          text-align: center;
          cursor: pointer;
        }

        .remark-label {
          color: #ff5500;
        }
      }

      .order-id {
        display: flex;
        align-items: center;
        font-size: 26rpx;
        color: #999999;
        margin-top: 10rpx;

        .id-label {
          display: inline-block;
          background-color: #3399ff;
          color: #ffffff;
          padding: 2rpx 8rpx;
          border-radius: 4rpx;
          margin-right: 10rpx;
        }

        .staff-app,
        .store-info {
          margin-left: 20rpx;
          color: #666666;
        }
      }
    }

    // 待付款提示区域样式
    .payment-pending-section {
      background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
      border-radius: 12rpx;
      padding: 12rpx 18rpx;
      margin: 12rpx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.15);
      border: 1rpx solid #FF9800;

      .payment-pending-info {
        display: flex;
        align-items: center;
        flex: 1;

        .payment-pending-icon {
          font-size: 28rpx;
          margin-right: 12rpx;
          color: #FF9800;
        }

        .payment-pending-message {
          font-size: 26rpx;
          color: #E65100;
          font-weight: 500;
        }
      }

      .payment-pending-btn {
        background: #ffffff;
        color: #FF9800;
        border: 1rpx solid #FF9800;
        border-radius: 20rpx;
        padding: 12rpx 28rpx;
        font-size: 26rpx;
        font-weight: 500;
        box-shadow: 0 1rpx 4rpx rgba(255, 152, 0, 0.1);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
          background: #FF9800;
          color: #ffffff;
        }
      }
    }

    // 分享接单按钮样式
    .share-order-btn {
      background: linear-gradient(135deg, #FFF8E1, #FFECB3);
      border-radius: 12rpx;
      padding: 20rpx 18rpx;
      margin: 12rpx 0;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 8rpx rgba(253, 209, 24, 0.15);
      border: 1rpx solid #fdd118;
      font-size: 26rpx;
      font-weight: 500;
      color: #E65100;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
        background: linear-gradient(135deg, #fdd118, #f0c419);
        color: #ffffff;
      }

      text {
        color: inherit;
        font-size: inherit;
        font-weight: inherit;
      }
    }

    .order-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 20rpx;
      gap: 20rpx;

      .action-btn {
        flex: 0 0 auto;
        height: 72rpx;
        padding: 0 40rpx;
        border-radius: 36rpx;
        font-size: 28rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
        }
      }



      .cancel-btn {
        background-color: #ffffff;
        color: #ff5500;
        border: 1rpx solid #ff5500;
      }

      .schedule-btn {
        background-color: #ffffff;
        color: #3399ff;
        border: 1rpx solid #3399ff;
      }

      .new-appointment-btn {
        background-color: #fdd118;
        color: #ffffff;
        border: 1rpx solid #fdd118;
      }

      // 改时/改价按钮样式 - 与其他按钮保持一致的高度和样式
      .edit-order-btn {
        background-color: #ffffff;
        color: #6c5ce7;
        border: 1rpx solid #6c5ce7;
      }

    }
  }

  // 共享到家政广场按钮容器 - 单独占一行
  .share-to-square-container {
    margin-top: 20rpx;
    padding: 0 30rpx 20rpx;

    .share-to-square-btn {
      width: 100%;
      height: 80rpx;
      background-color: #ff9800;
      color: #ffffff;
      border: 1rpx solid #ff9800;
      border-radius: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-size: 30rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &:active {
        background-color: #f57c00;
        border-color: #f57c00;
        transform: scale(0.98);
      }

      // 已共享状态的样式
      &.shared {
        background-color: #4caf50;
        border-color: #4caf50;

        &:active {
          background-color: #388e3c;
          border-color: #388e3c;
        }
      }

      text {
        font-size: 30rpx;
        font-weight: 500;
      }
    }
  }



// 关闭订单弹窗样式
.close-order-popup {
  padding: 40rpx 30rpx;

  .popup-title {
    text-align: center;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 40rpx;
  }

  .popup-content {
    text-align: center;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 40rpx;
    line-height: 1.5;
  }

  .price-input-box {
    position: relative;
    margin-bottom: 40rpx;
    border-bottom: 1rpx solid #eee;
    padding-bottom: 10rpx;

    .currency-symbol {
      position: absolute;
      right: 10rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 32rpx;
      color: #333;
    }

    .price-input {
      width: 100%;
      height: 80rpx;
      font-size: 32rpx;
      text-align: right;
      padding-right: 60rpx;
    }
  }

  .popup-buttons {
    display: flex;
    justify-content: space-between;

    .cancel-button,
    .confirm-button {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 32rpx;
      border-radius: 8rpx;
      transition: all 0.2s ease;
    }

    .cancel-button {
      color: #666;
      margin-right: 20rpx;

      &:active {
        background-color: #f5f5f5;
      }
    }

    .confirm-button {
      background-color: #fdd118;
      color: #fff;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 搜索框清空按钮样式
.search-box {
  .search-input {
    .search-clear {
      width: 40rpx;
      height: 40rpx;
      border-radius: 20rpx;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 10rpx;
      transition: all 0.3s ease;

      &:active {
        background: #e0e0e0;
        transform: scale(0.9);
      }
    }
  }
}

// 加载状态样式
.loading-container {
  padding: 20rpx 20rpx 60rpx; // 增加底部间距，防止内容被截断

  .traditional-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50rpx 0;

    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0 140rpx; // 增加底部间距

  .empty-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999;
  }
}

// 骨架屏样式
.skeleton-order-item {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .skeleton-content {
    .skeleton-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20rpx;
    }
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 6rpx;
  margin-bottom: 12rpx;

  &.skeleton-time {
    width: 200rpx;
    height: 28rpx;
  }

  &.skeleton-status {
    width: 120rpx;
    height: 28rpx;
  }

  &.skeleton-type {
    width: 180rpx;
    height: 32rpx;
  }

  &.skeleton-product {
    width: 160rpx;
    height: 28rpx;
  }

  &.skeleton-service-time {
    width: 240rpx;
    height: 28rpx;
  }

  &.skeleton-customer {
    width: 200rpx;
    height: 28rpx;
  }

  &.skeleton-address {
    width: 300rpx;
    height: 28rpx;
  }

  &.skeleton-order-id {
    width: 150rpx;
    height: 24rpx;
  }

  &.skeleton-store {
    width: 100rpx;
    height: 24rpx;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;

  .loading-more-text {
    margin-left: 10rpx;
    font-size: 26rpx;
    color: #999;
  }
}

.no-more-data {
  text-align: center;
  padding: 30rpx 0;
  font-size: 26rpx;
  color: #999;
}

// 自定义日期选择器样式
.custom-date-picker {
  padding: 40rpx 50rpx;

  .picker-header {
    text-align: center;
    margin-bottom: 40rpx;

    .picker-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .date-range-container {
    margin-bottom: 40rpx;

    .date-item {
      margin-bottom: 30rpx;

      .date-label {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 15rpx;
      }

      .date-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 25rpx;
        background-color: #f8f8f8;
        border-radius: 12rpx;
        border: 1rpx solid #e8e8e8;

        .date-text {
          font-size: 28rpx;
          color: #333;

          &:empty::before {
            content: attr(placeholder);
            color: #999;
          }
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .picker-buttons {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;

    .picker-cancel,
    .picker-confirm {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 32rpx;
      border-radius: 12rpx;
      transition: all 0.2s ease;
    }

    .picker-cancel {
      color: #666;
      background-color: #f5f5f5;

      &:active {
        background-color: #e8e8e8;
      }
    }

    .picker-confirm {
      background-color: #fdd118;
      color: #fff;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 改时改价弹窗样式 - 恢复版本
.edit-order-modal {
  padding: 20rpx;

  .order-info-section {
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 28rpx;
        color: #666;
      }

      .info-value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;

        &.amount {
          color: #fdd118;
          font-weight: 600;
        }
      }
    }
  }

  .edit-section {
    margin-bottom: 24rpx;

    .section-title {
      font-size: 30rpx;
      color: #333;
      font-weight: 600;
      margin-bottom: 16rpx;
    }

    // 表单项包装器
    .form-item-wrapper {
      margin-bottom: 16rpx;
      
      .input-prefix {
        background: #fdd118;
        color: #333;
        font-size: 28rpx;
        font-weight: bold;
        padding: 0 12rpx;
        border-radius: 6rpx 0 0 6rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }

      // 确保所有 u-input 有相同的背景色
      ::v-deep .u-input {
        background-color: #ffffff !important;
        
        .u-input__content {
          background-color: #ffffff !important;
        }
        
        &.u-input--disabled,
        &.u-input--readonly {
          background-color: #ffffff !important;
          
          .u-input__content {
            background-color: #ffffff !important;
            color: #333 !important;
          }
        }
      }
    }

    // 时间选择器容器
    .time-selectors-container {
      .form-item-wrapper {
        margin-bottom: 16rpx;
        
        &:last-child {
          margin-bottom: 12rpx;
        }
      }
    }

    .input-hint {
      font-size: 24rpx;
      color: #999;
      line-height: 1.5;
      padding: 0 8rpx;
    }
  }

  .warning-tip {
    background: #fff3cd;
    border: 1rpx solid #ffeaa7;
    border-radius: 8rpx;
    padding: 16rpx;
    display: flex;
    align-items: center;

    .warning-icon {
      font-size: 24rpx;
      margin-right: 8rpx;
    }

    .warning-text {
      font-size: 26rpx;
      color: #856404;
      margin-left: 8rpx;
      line-height: 1.4;
    }
  }
}

// 扫码支付弹窗样式
.qrcode-modal-content {
  padding: 40rpx;
  text-align: center;

  .qrcode-title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 40rpx;
    font-weight: 500;
  }

  .qrcode-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30rpx;
    padding: 20rpx;
    background-color: #f8f8f8;
    border-radius: 12rpx;

    .qrcode-canvas {
      border: 2rpx solid #e0e0e0;
      border-radius: 8rpx;
    }

    .qrcode-image {
      border: 2rpx solid #e0e0e0;
      border-radius: 8rpx;
      background: #ffffff;
    }
  }

  .qrcode-amount {
    font-size: 36rpx;
    color: #ff6b35;
    font-weight: 600;
    margin-bottom: 30rpx;
  }

  .qrcode-tips {
    margin-bottom: 40rpx;

    text {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 8rpx;
    }
  }

  .copy-link-section {
    .copy-link-btn {
      width: 100%;
      height: 80rpx;
      background-color: #007AFF;
      color: #fff;
      border: none;
      border-radius: 12rpx;
      font-size: 32rpx;
      font-weight: 500;

      &:active {
        opacity: 0.8;
      }
    }
  }




  }
}

// 共享订单操作区域样式
.shared-order-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;
}

.shared-status-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
}

.shared-status-text {
  font-size: 24rpx;
  color: #28a745;
  font-weight: 500;
}

.adjust-commission-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e55a2b, #de831a);
  }
}

// 佣金调整弹窗样式
.adjust-commission-popup {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.commission-adjust-section {
  margin-bottom: 30rpx;
}

.current-commission {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.section-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.current-amount {
  font-size: 28rpx;
  color: #ff6b35;
  font-weight: 600;
}

.new-commission {
  .section-label {
    display: block;
    margin-bottom: 16rpx;
  }
}

// 共享设置弹窗样式
.share-setting-popup {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.popup-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

// 订单信息样式
.order-info-section {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;

  &.amount {
    color: #ff6b35;
    font-weight: 600;
    font-size: 28rpx;
  }
}

// 设置区域样式
.setting-section {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.input-container {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #fdd118;
    background: #fff;
  }
}

.currency-symbol {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.amount-input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  border: none;
  background: transparent;
  outline: none;
}

.input-tip {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

// 时间选项样式
.time-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12rpx;
}

.time-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;

  &.active {
    border-color: #fdd118;
    background: #fff9e6;
  }

  &:active {
    transform: scale(0.98);
  }
}

.option-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.option-radio {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &.checked {
    border-color: #fdd118;
    background: #fdd118;
  }
}

.radio-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #fff;
}

// 自定义时间样式
.custom-time-section {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.custom-input-row {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #fdd118;
    background: #fff;
  }
}

.custom-time-input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  border: none;
  background: transparent;
  outline: none;
}

.time-unit {
  font-size: 26rpx;
  color: #666;
  margin-left: 8rpx;
}

.custom-tip {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

// 弹窗按钮样式
.popup-buttons {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.popup-cancel,
.popup-confirm {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.popup-cancel {
  color: #666;
  border-right: 1rpx solid #f0f0f0;

  &:active {
    background: #f8f9fa;
  }
}

.popup-confirm {
  color: #fdd118;

  &:active {
    background: #fff9e6;
  }
}
</style>

