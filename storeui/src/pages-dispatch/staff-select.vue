<template>
  <view class="container">
    <!-- 顶部背景区域 -->
    <view class="header-background-section">
      <view class="header-background"></view>
      <view class="header-content">
        <!-- 顶部导航栏 -->
        <view class="header-section">
          <view class="navbar">
            <view class="nav-back" @tap="goBack">
              <u-icon name="arrow-left" color="#fff" size="20"></u-icon>
            </view>
            <text class="nav-title">选择人员</text>
            <view class="nav-placeholder"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 通知提示 -->
    <!-- <view class="notice-bar">
      <text>优先使用微信通知，短信通知次之(消耗1条短信)，短信余额不足则通知失败，但不影响派单。</text>
      <view class="close-btn" @tap="closeNotice">
        <u-icon name="close" color="#999" size="16"></u-icon>
      </view>
    </view> -->

    <!-- 搜索框 -->
    <view class="search-section">
      <view class="search-box">
        <u-icon name="search" size="16" color="#999"></u-icon>
        <input
          class="search-input"
          type="text"
          placeholder="搜索服务人员"
          v-model="searchText"
          @input="onSearchInput"
        />
        <view class="search-clear" v-if="searchText" @click="clearSearch">
          <u-icon name="close-circle" size="14" color="#999"></u-icon>
        </view>
      </view>
    </view>

    <!-- 筛选条件 -->
    <!-- <view class="filter-section">
      <view class="filter-container">
        <view class="filter-item">
          <text>员工类型</text>
          <u-icon name="arrow-down" size="16" color="#666"></u-icon>
        </view>
        <view class="filter-item">
          <text>入驻状态</text>
          <u-icon name="arrow-down" size="16" color="#666"></u-icon>
        </view>
        <view class="filter-checkbox">
          <checkbox :checked="hideUnavailable" @tap="toggleHideUnavailable" />
          <text>隐藏忙碌人员</text>
        </view>
      </view>
    </view> -->

    <!-- 员工列表 -->
    <scroll-view class="staff-list-container" scroll-y="true" :style="{ height: scrollViewHeight }">
      <!-- 加载状态 -->
      <view class="loading-container" v-if="loading">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在获取可派单人员...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-container" v-else-if="!loading && staffList.length === 0">
        <view class="empty-icon">👥</view>
        <text class="empty-text">暂无可派单人员</text>
        <text class="empty-desc">该产品暂时没有可服务的人员</text>
      </view>

      <!-- 有数据时显示列表 -->
      <view class="staff-list" v-else>


        <!-- 搜索无结果提示 -->
        <view class="no-results-container" v-if="searchText && filteredStaffList.length === 0">
          <view class="no-results-icon">🔍</view>
          <text class="no-results-text">未找到相关服务人员</text>
          <text class="no-results-desc">请尝试其他关键词</text>
        </view>

        <!-- 员工列表 -->
        <view
          class="staff-item"
          v-for="(staff, index) in filteredStaffList"
          v-if="staff && staff.id"
          :key="getStaffKey(staff, index)"
          :class="{ selected: isStaffSelected(staff) }"
          @tap="handleStaffTap"
          :data-staff-index="getOriginalIndex(staff)"
        >
          <view class="select-icon" :class="{ selected: isStaffSelected(staff) }">
            <view class="circle" v-if="!isStaffSelected(staff)"></view>
            <u-icon name="checkmark" color="#fff" size="16" v-else></u-icon>
          </view>
          <view class="staff-avatar">
            <image
              :src="staff.avatar || 'https://jingang.obs.cn-east-3.myhuaweicloud.com/jgstore/static/img/logo.png'"
              class="avatar-img"
              mode="aspectFill"
            />
          </view>
          <view class="staff-info">
            <view class="staff-header">
              <view class="staff-name-section">
                <view class="staff-tag">{{ staff.tag || '普通' }}</view>
                <view class="staff-name">{{ staff.name || '未知' }}</view>
              </view>
              <view class="staff-rating">
                <u-icon name="star-fill" color="#FFC107" size="14"></u-icon>
                <text>{{ staff.rating || '0.0' }}</text>
              </view>
            </view>
            <view class="staff-details">
              <view class="detail-item">
                <text class="detail-label">状态：</text>
                <text class="detail-value">{{ staff.status || '正常' }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">服务：</text>
                <text class="detail-value">{{ staff.service_cnt || '0' }}次</text>
              </view>
              <view class="detail-item" v-if="staff.age">
                <text class="detail-label">年龄：</text>
                <text class="detail-value">{{ staff.age }}岁</text>
              </view>
              <view class="detail-item" v-if="staff.sex_name">
                <text class="detail-label">性别：</text>
                <text class="detail-value">{{ staff.sex_name }}</text>
              </view>
            </view>
            <view class="staff-contact">
              <view class="contact-phone">
                <u-icon name="phone" color="#666" size="14"></u-icon>
                <text>{{ staff.phone || '' }}</text>
              </view>
              <view
                class="call-btn"
                @tap.stop="handleCallStaff"
                :data-staff-index="index"
              >
                <u-icon name="phone-fill" color="#fff" size="16"></u-icon>
                <text>拨打</text>
              </view>
            </view>
            <view class="staff-address" v-if="staff.address">
              <u-icon name="home" color="#666" size="14"></u-icon>
              <text>{{ staff.address }}</text>
            </view>
          </view>
        </view>

        <!-- 列表底部提示 -->
        <view class="list-footer" v-if="staffList.length > 0">
          <text>已经到头了~</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="selected-info" v-if="selectedStaff">
        <text class="selected-text">已选择：{{ selectedStaff.name || '未知' }}</text>
      </view>
      <view class="action-area">
        <view class="next-btn primary-btn" @tap="goToNextStep" :class="{ disabled: !selectedStaff }">
          <text>确认派单</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchText: '',
      hideUnavailable: false,
      notifyStaff: true,
      selectedStaff: null,
      staffList: [],
      orderNumber: '',
      orderInfo: null,
      loading: false,
      isRedispatch: false,
      searchTimer: null // 搜索防抖定时器
    };
  },
  computed: {
    // 过滤有效的员工数据并支持搜索
    filteredStaffList() {
      // 确保staffList是数组且包含有效数据
      let validStaffList = Array.isArray(this.staffList)
        ? this.staffList.filter(staff => staff && (staff.id || staff.uuid))
        : [];

      // 如果没有搜索文本，返回所有有效员工
      if (!this.searchText || this.searchText.trim() === '') {
        return validStaffList;
      }

      // 搜索过滤
      const searchTerm = this.searchText.trim().toLowerCase();
      return validStaffList.filter(staff => {
        const name = (staff.name || '').toLowerCase();
        const phone = (staff.phone || '').toLowerCase();
        return name.includes(searchTerm) || phone.includes(searchTerm);
      });
    },
    // 计算滚动视图高度
    scrollViewHeight() {
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight || 0;
      const screenHeight = systemInfo.screenHeight || 667;

      // 计算各部分高度（单位：px）
      const headerHeight = statusBarHeight + 44; // 状态栏 + 导航栏
      const bottomBarHeight = 80; // 底部操作栏
      const headerBackgroundHeight = 120; // 顶部背景区域

      // 计算可用高度
      const availableHeight = screenHeight - headerHeight - bottomBarHeight - headerBackgroundHeight;

      return `${availableHeight}px`;
    }
  },
  onLoad(options) {
    console.log('页面参数:', options);

    // 获取订单号和重派标识
    if (options.orderNumber) {
      this.orderNumber = options.orderNumber;
      this.isRedispatch = options.redispatch === 'true';

      // 获取可派单人员列表
      this.getDispatchableStaff();
    } else {
      uni.showToast({
        title: '缺少订单信息',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 处理员工点击事件
    handleStaffTap(event) {
      const index = event.currentTarget.dataset.staffIndex;
      if (index !== undefined && this.staffList[index]) {
        const staff = this.staffList[index];
        this.selectStaff(staff);
      }
    },
    // 判断员工是否被选中
    isStaffSelected(staff) {
      if (!staff || !this.selectedStaff) return false;
      return this.selectedStaff.id === staff.id;
    },
    // 生成员工列表项的key
    getStaffKey(staff, index) {
      if (!staff) return `staff-${index}`;
      return `staff-${staff.uuid || staff.id || index}`;
    },
    // 获取可派单人员列表
    async getDispatchableStaff() {
      if (!this.orderNumber) {
        console.error('订单号为空');
        return;
      }

      this.loading = true;

      try {
        const response = await this.$get('/api/v1/order/getDispatchableStaff', {
          order_number: this.orderNumber
        });

        console.log('获取可派单人员响应:', response);

        if (response && response.staff_list) {
          // 保存订单信息，包含新增的字段
          this.orderInfo = {
            ...response.order_info,
            // 确保包含所有必要字段
            order_amount: response.order_info?.order_amount || 0,
            default_commission: response.order_info?.default_commission || 0,
            customer_name: response.order_info?.customer_name || '',
            customer_mobile: response.order_info?.customer_mobile || '',
            service_address: response.order_info?.service_address || '',
            service_time: response.order_info?.service_time || ''
          };

          // 处理人员列表数据，添加数据验证
          this.staffList = response.staff_list
            .filter(staff => staff && (staff.id || staff.uuid)) // 过滤无效数据
            .map((staff, index) => {
              const processedStaff = {
                id: staff.id || staff.uuid || `temp_${Date.now()}_${index}`,
                uuid: staff.uuid || staff.id || `temp_${Date.now()}_${index}`,
                name: staff.real_name || staff.nick_name || '未知',
                tag: staff.work_type_name || '普通',
                rating: staff.star_level || '0.0',
                serviceType: '服务人员',
                phone: staff.mobile || '',
                address: staff.address || '',
                scheduleTime: '可服务',
                status: staff.status_name || '正常',
                avatar: staff.avatar || '',
                service_cnt: staff.service_cnt || '0',
                age: staff.age || '',
                sex_name: staff.sex_name || ''
              };
              console.log(`处理员工${index}:`, processedStaff);
              return processedStaff;
            });

          console.log('处理后的人员列表:', this.staffList);

          if (this.staffList.length === 0) {
            uni.showToast({
              title: '暂无可派单人员',
              icon: 'none'
            });
          }
        } else {
          uni.showToast({
            title: '获取人员列表失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取可派单人员失败:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 派单确认
    async assignStaff() {
      if (!this.selectedStaff) {
        uni.showToast({
          title: '请选择服务人员',
          icon: 'none'
        });
        return;
      }

      // 派单前验证员工状态
      if (this.selectedStaff.status !== '1') {
        uni.showToast({
          title: '该员工状态不可用，无法派单',
          icon: 'none'
        });
        return;
      }

      uni.showLoading({
        title: '派单中...'
      });

      try {
        const response = await this.$post('/api/v1/order/assignStaff', {
          order_number: this.orderNumber,
          staff_uuid: this.selectedStaff.uuid,
          notify_staff: this.notifyStaff
        });

        console.log('派单响应:', response);

        uni.hideLoading();

        uni.showToast({
          title: '派单成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);

      } catch (error) {
        console.error('派单失败:', error);
        uni.hideLoading();

        let errorMsg = '派单失败，请重试';
        if (error.response && error.response.data && error.response.data.msg) {
          errorMsg = error.response.data.msg;
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
      }
    },
    goBack() {
      uni.navigateBack();
    },
    closeNotice() {
      // 关闭通知栏
      uni.showToast({
        title: '已关闭通知',
        icon: 'none',
      });
    },
    // 搜索输入处理（带防抖）
    onSearchInput() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，300ms后执行搜索
      this.searchTimer = setTimeout(() => {
        console.log('搜索文本:', this.searchText);
        // 搜索逻辑已在 computed 中的 filteredStaffList 处理
      }, 300);
    },

    // 清空搜索
    clearSearch() {
      this.searchText = '';
      console.log('清空搜索');
    },

    // 获取员工在原始列表中的索引
    getOriginalIndex(staff) {
      return this.staffList.findIndex(item => item && item.id === staff.id);
    },
    toggleHideUnavailable() {
      this.hideUnavailable = !this.hideUnavailable;
      // 根据选项筛选员工列表
      console.log('隐藏忙碌人员:', this.hideUnavailable);
    },
    selectStaff(staff) {
      if (!staff) return;
      this.selectedStaff = staff;
    },
    // 处理拨打电话按钮点击
    handleCallStaff(event) {
      const index = event.currentTarget.dataset.staffIndex;

      if (index !== undefined && this.staffList[index]) {
        const staff = this.staffList[index];
        this.callStaff(staff);
      } else {
        uni.showToast({
          title: '获取员工信息失败',
          icon: 'none'
        });
      }
    },

    callStaff(staff) {
      if (!staff) {
        uni.showToast({
          title: '员工信息不存在',
          icon: 'none'
        });
        return;
      }

      const phoneNumber = staff.phone || staff.mobile || '';

      if (!phoneNumber) {
        uni.showToast({
          title: '该员工暂无电话号码',
          icon: 'none'
        });
        return;
      }

      // 验证电话号码格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phoneNumber)) {
        uni.showToast({
          title: '电话号码格式不正确',
          icon: 'none'
        });
        return;
      }

      uni.makePhoneCall({
        phoneNumber: phoneNumber,
        fail: (err) => {
          console.error('拨打电话失败:', err);
          uni.showToast({
            title: '拨打电话失败',
            icon: 'none'
          });
        }
      });
    },
    setAsLeader(staff) {
      if (!staff) return;
      // 设置为队长
      uni.showToast({
        title: `已将${staff.name || '未知'}设为队长`,
        icon: 'none',
      });
    },
    toggleNotifyStaff() {
      this.notifyStaff = !this.notifyStaff;
      console.log('通知服务人员派单:', this.notifyStaff);
    },
    goToNextStep() {
      if (!this.selectedStaff) {
        uni.showToast({
          title: '请选择服务人员',
          icon: 'none'
        });
        return;
      }

      // 跳转到设置提成页面，传递订单号和选中的员工信息
      const staffData = encodeURIComponent(JSON.stringify(this.selectedStaff));
      const orderData = encodeURIComponent(JSON.stringify(this.orderInfo));
      uni.navigateTo({
        url: `/pages-dispatch/commission-setting?orderNumber=${this.orderNumber}&staffData=${staffData}&orderData=${orderData}&isRedispatch=${this.isRedispatch}`,
        fail: (err) => {
          console.error('跳转设置提成页面失败:', err);
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none'
          });
        }
      });
    },
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
}

// 顶部背景区域
.header-background-section {
  position: relative;
  padding-bottom: 40rpx;
  margin-bottom: 30rpx;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 20rpx 30rpx 0;
  }
}

// 顶部导航区域
.header-section {
  padding-top: var(--status-bar-height);
}

.navbar {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  position: relative;
}

.nav-back,
.nav-placeholder {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
}

.nav-placeholder {
  background: transparent;
  border: none;
}

.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  pointer-events: none;
}

/* 通知提示栏 */
.notice-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #fff9e6;
  color: #ff9800;
  font-size: 24rpx;
}

.close-btn {
  padding: 10rpx;
}

// 搜索区域
.search-section {
  margin: 20rpx 30rpx;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 24rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  border: none;
  background: transparent;
  outline: none;

  &::placeholder {
    color: #999;
  }
}

.search-clear {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:active {
    background: #e0e0e0;
    transform: scale(0.9);
  }
}

// 筛选条件
.filter-section {
  margin: 20rpx 30rpx;
}

.filter-container {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 28rpx;
  color: #333;
  padding: 12rpx 20rpx;
  border-radius: 30rpx;
  background: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  u-icon {
    margin-left: 6rpx;
  }
}

.filter-checkbox {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;

  checkbox {
    transform: scale(0.8);
    margin-right: 6rpx;
  }
}

// 员工列表
.staff-list-container {
  flex: 1;
  background: #fff;
  overflow: hidden;
}

.staff-list {
  padding: 0 30rpx;
}

.staff-item {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #f0f0f0;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;

  &.selected {
    border-color: #fdd118;
    box-shadow: 0 4rpx 20rpx rgba(253, 209, 24, 0.15);
    background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
  }

  &:active {
    background: #fafafa;
    transform: scale(0.98);
    box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  }
}

.select-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  margin-top: 5rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &.selected {
    background-color: #fdd118;
    border-color: #fdd118;
  }
}

.circle {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #fff;
}

// 头像样式
.staff-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 24rpx;
  flex-shrink: 0;

  .avatar-img {
    width: 100%;
    height: 100%;
    border-radius: 60rpx;
    border: 2rpx solid #f0f0f0;
  }
}

.staff-info {
  flex: 1;
  min-width: 0;
}

.staff-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.staff-name-section {
  flex: 1;
}

.staff-tag {
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  margin-bottom: 8rpx;
  display: inline-block;
  font-weight: 500;
}

.staff-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.staff-rating {
  display: flex;
  align-items: center;
  gap: 4rpx;
  font-size: 24rpx;
  color: #666;
  margin-top: 4rpx;
}

// 详细信息样式
.staff-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;

  .detail-label {
    color: #999;
    margin-right: 4rpx;
  }

  .detail-value {
    color: #666;
    font-weight: 500;
  }
}

// 联系方式样式
.staff-contact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.contact-phone {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.call-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #fdd118 0%, #ff801b 100%);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: #fff;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 10;
  pointer-events: auto;

  &:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #e6c015, #e6721a);
  }

  &:hover {
    opacity: 0.9;
  }
}

// 地址样式
.staff-address {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  color: #999;
  line-height: 1.3;
}

// 移除重复的样式，已在上面重新定义

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #fdd118;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
}

// 搜索无结果状态
.no-results-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.no-results-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.no-results-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.no-results-desc {
  font-size: 24rpx;
  color: #999;
}

.list-footer {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 底部操作栏 */
.bottom-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.selected-info {
  flex: 1;

  .selected-text {
    font-size: 26rpx;
    color: #666;
  }
}

.action-area {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.next-btn {
  height: 80rpx;
  padding: 0 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  min-width: 160rpx;

  &.primary-btn {
    background: linear-gradient(135deg, #fdd118, #ff801c);
    border: 1px solid #fdd118;

    text {
      color: #ffffff;
      font-weight: 600;
    }

    &:active {
      background: linear-gradient(135deg, #e6c015, #e6721a);
      transform: scale(0.98);
    }

    &.disabled {
      background: #f0f0f0;
      border-color: #f0f0f0;

      text {
        color: #ccc;
      }

      &:active {
        transform: none;
        background: #f0f0f0;
      }
    }
  }
}
</style>
